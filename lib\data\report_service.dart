import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/report.dart';

class ReportService {
  final String baseUrl;

  ReportService(this.baseUrl);

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  Future<Map<String, String>> _buildHeaders() async {
    final token = await _getToken();
    if (token == null) throw AuthenticationException('No authentication token found');
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Fetch report between [start] and [end] (inclusive dates)
  Future<ReportData> fetchReport({
    required DateTime start,
    required DateTime end,
  }) async {
    if (start.isAfter(end)) {
      throw ArgumentException('Start date cannot be after end date');
    }

    try {
      final headers = await _buildHeaders();
      final formatter = DateFormat('yyyy-MM-dd');
      final s = formatter.format(start);
      final e = formatter.format(end);
      final uri = Uri.parse('$baseUrl/reports?start=$s&end=$e');

      if (kDebugMode) {
        print('Fetching report from: $uri');
      }

      final res = await http.get(uri, headers: headers).timeout(
            const Duration(seconds: 15),
            onTimeout: () => throw NetworkException('Request timed out'),
          );

      if (res.statusCode == 200) {
        try {
          final json = jsonDecode(res.body) as Map<String, dynamic>;
          return ReportData.fromJson(json);
        } catch (e) {
          throw DataParsingException('Invalid response format: $e');
        }
      } else if (res.statusCode == 401) {
        throw AuthenticationException('Unauthorized: Invalid or expired token');
      } else if (res.statusCode == 400) {
        throw ArgumentException('Bad request: Invalid date range or parameters');
      } else {
        throw NetworkException('Failed to fetch report: ${res.statusCode}');
      }
    } catch (e) {
      if (e is AuthenticationException || e is ArgumentException || e is NetworkException || e is DataParsingException) {
        rethrow;
      }
      throw ReportServiceException('An unexpected error occurred: $e');
    }
  }
}

class AuthenticationException implements Exception {
  final String message;
  AuthenticationException(this.message);
  @override
  String toString() => message;
}

class ArgumentException implements Exception {
  final String message;
  ArgumentException(this.message);
  @override
  String toString() => message;
}

class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
  @override
  String toString() => message;
}

class DataParsingException implements Exception {
  final String message;
  DataParsingException(this.message);
  @override
  String toString() => message;
}

class ReportServiceException implements Exception {
  final String message;
  ReportServiceException(this.message);
  @override
  String toString() => message;
}