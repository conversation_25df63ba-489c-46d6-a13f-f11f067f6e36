class User {
  final int id;
  final String name;
  final String username;
  String password;

  User({
    required this.id,
    required this.name,
    required this.username,
    this.password = '',
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'] ?? 0,
        name: json['full_name'] ?? '',
        username: json['username'] ?? '',
        password: json['password'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'full_name': name,
        'username': username,
        'password': password,
      };
}
