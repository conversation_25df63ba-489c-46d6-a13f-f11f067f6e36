/// lib/models/billiard_order_detail.dart
library;

import 'package:flutter/material.dart';

class BilliardOrderDetail {
  final int id;
  final int tableId;
  final int sessionId;
  final int packageId;
  final int duration;      // in hours
  final double price;
  final double discount;
  final int status;
  final int? typePackagePriceId; // ← baru
  final TimeOfDay? startTime;
  final TimeOfDay? endTime;
  final String tableNumber;

  BilliardOrderDetail({
    required this.id,
    required this.tableId,
    required this.sessionId,
    required this.packageId,
    required this.duration,
    required this.price,
    required this.discount,
    required this.status,
    this.typePackagePriceId,
    this.startTime,
    this.endTime,
    this.tableNumber = '',
  });

  factory BilliardOrderDetail.fromJson(Map<String, dynamic> json) {
    // Parse a time string in ISO or HH:mm:ss
    TimeOfDay? parseTime(String? s) {
      if (s == null || s.isEmpty) return null;
      try {
        final dt = DateTime.parse(s);
        return TimeOfDay(hour: dt.hour, minute: dt.minute);
      } catch (_) {
        final parts = s.split(':');
        if (parts.length >= 2) {
          final h = int.tryParse(parts[0]) ?? 0;
          final m = int.tryParse(parts[1]) ?? 0;
          return TimeOfDay(hour: h, minute: m);
        }
        return null;
      }
    }

    // 1) parse the start time
    final rawStart = parseTime(json['start_time'] as String?);

    // 2) compute endTime = startTime + duration hours
    TimeOfDay? computedEnd;
    if (rawStart != null) {
      final totalMinutes = rawStart.hour * 60 +
          rawStart.minute +
          (json['duration'] as int) * 60;
      final endHour = (totalMinutes ~/ 60) % 24;
      final endMinute = totalMinutes % 60;
      computedEnd = TimeOfDay(hour: endHour, minute: endMinute);
    }

    return BilliardOrderDetail(
      id: json['id'] as int,
      tableId: json['table_id'] as int,
      sessionId: json['session_id'] as int,
      packageId: json['package_id'] as int,
      duration: json['duration'] as int,
      price: (json['price'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      status: json['status'] is int
          ? json['status'] as int
          : int.tryParse(json['status'].toString()) ?? 0,
      typePackagePriceId: json['type_package_price_id'] != null
          ? (json['type_package_price_id'] as num).toInt()
          : null,
      startTime: rawStart,
      endTime: computedEnd,
      tableNumber: json['table_number'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    String fmt(TimeOfDay t) {
      final h = t.hour.toString().padLeft(2, '0');
      final m = t.minute.toString().padLeft(2, '0');
      return '$h:$m:00';
    }

    final data = <String, dynamic>{
      'id': id,
      'table_id': tableId,
      'session_id': sessionId,
      'package_id': packageId,
      'duration': duration,
      'price': price,
      'discount': discount,
      'status': status,
      'table_number': tableNumber,
    };
    if (typePackagePriceId != null) {
      data['type_package_price_id'] = typePackagePriceId;
    }
    if (startTime != null) {
      data['start_time'] = fmt(startTime!);
    }
    if (endTime != null) {
      data['end_time'] = fmt(endTime!);
    }
    return data;
  }
}
