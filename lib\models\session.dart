import 'food_order_detail.dart';
import 'billiard_order_detail.dart';

class Session {
  final int id;
  final String guestName;
  final String? guestPhone;
  final String invoiceNumber;
  final DateTime date;
  final String status; // 'pending' | 'paid' | 'cancelled'
  final double totalFood;
  final double totalBilliard;
  final double discountPercent; // % discount applied
  final double discountAmount; // computed by MySQL
  final double taxRate; // % tax rate applied
  final double taxAmount; // computed by MySQL
  final double ppnRate; // ← NEW: percent
  final double ppnAmount; // ← NEW: computed amount
  final double totalAmount; // computed by MySQL
  final DateTime? paidAt; // null until paid
  final DateTime? printedAt; // null until printed
  final String? paymentMethod; // 'cash' | 'debit' | 'qris'
  final double payAmount;
  final List<FoodOrderDetail> foodDetails;
  final List<BilliardOrderDetail> billiardDetails;
  final DateTime? createdAt;
  final int? handledByUserId;
  final String? handledByUserName;
  final int? cashShiftId;

  Session({
    required this.id,
    required this.guestName,
    this.guestPhone,
    required this.invoiceNumber,
    required this.date,
    this.status = 'pending',
    this.totalFood = 0,
    this.totalBilliard = 0,
    this.discountPercent = 0,
    this.discountAmount = 0,
    this.taxRate = 0,
    this.taxAmount = 0,
    this.ppnRate = 0, // ← init to 0
    this.ppnAmount = 0, // ← init to 0
    this.totalAmount = 0,
    this.paidAt,
    this.printedAt,
    this.paymentMethod,
    this.payAmount = 0,
    this.foodDetails = const [],
    this.billiardDetails = const [],
    this.createdAt,
    this.handledByUserId,
    this.handledByUserName,
    this.cashShiftId,
  });

  factory Session.fromJson(Map<String, dynamic> json) {
    // parse paidAt
    DateTime? paid;
    if (json['paid_at'] != null) {
      paid = DateTime.parse(json['paid_at'] as String).toLocal();
    }
    // parse printedAt
    DateTime? printed;
    if (json['printed_at'] != null) {
      printed = DateTime.parse(json['printed_at'] as String).toLocal();
    }

    // parse food items
    final rawFood = json['food_items'];
    final foodList = <FoodOrderDetail>[];
    if (rawFood is List) {
      for (var e in rawFood) {
        foodList.add(FoodOrderDetail.fromJson(e as Map<String, dynamic>));
      }
    }

    // parse billiard items
    final rawBil = json['billiard_orders'];
    final bilList = <BilliardOrderDetail>[];
    if (rawBil is List) {
      for (var e in rawBil) {
        bilList.add(BilliardOrderDetail.fromJson(e as Map<String, dynamic>));
      }
    }

    return Session(
      id: json['id'] as int,
      guestName: json['guest_name'] as String,
      guestPhone: json['guest_phone'] as String?,
      invoiceNumber: json['invoice_number'] as String,
      date: DateTime.parse(json['date'] as String).toLocal(),
      status: json['status'] as String? ?? 'pending',
      totalFood: double.tryParse(json['total_food'].toString()) ?? 0,
      totalBilliard: double.tryParse(json['total_billiard'].toString()) ?? 0,
      discountPercent: double.tryParse(json['discount_percent'].toString()) ?? 0,
      discountAmount: double.tryParse(json['discount_amount'].toString()) ?? 0,
      taxRate: double.tryParse(json['tax_rate'].toString()) ?? 0,
      taxAmount: double.tryParse(json['tax_amount'].toString()) ?? 0,
      ppnRate: double.tryParse(json['ppn_rate'].toString()) ?? 0, // ← parse from JSON
      ppnAmount: double.tryParse(json['ppn_amount'].toString()) ?? 0, //
      totalAmount: double.tryParse(json['total_amount'].toString()) ?? 0,
      paidAt: paid,
      printedAt: printed,
      paymentMethod: json['payment_method'] as String?,
      payAmount: double.tryParse(json['pay_amount'].toString()) ?? 0,
      foodDetails: foodList,
      billiardDetails: bilList,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String).toLocal() : null,
      handledByUserId: json['handled_by_user_id'] as int?,
      handledByUserName: json['handled_by_user_name'] as String?,
      cashShiftId: json['cash_shift_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
        'guest_name': guestName,
        'guest_phone': guestPhone,
        'invoice_number': invoiceNumber,
        'date': date.toIso8601String(),
        'status': status,
        'total_food': totalFood,
        'total_billiard': totalBilliard,
        'discount_percent': discountPercent,
        // discount_amount, tax_amount, total_amount computed server-side
        // tax_amount computed server-side
        'ppn_rate': ppnRate, // ← send to backend
        'tax_rate': taxRate,
        'payment_method': paymentMethod,
        'pay_amount': payAmount,
        'food_items': foodDetails.map((f) => f.toJson()).toList(),
        'billiard_orders': billiardDetails.map((b) => b.toJson()).toList(),
        'handled_by_user_id': handledByUserId,
        'handled_by_user_name': handledByUserName,
        'cash_shift_id': cashShiftId,
      };
}
