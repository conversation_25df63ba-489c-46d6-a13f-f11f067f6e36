import 'package:intl/intl.dart';

class ReportData {
  final ReportSummary summary;
  final List<DailyCount> daily;
  final List<BilliardDetail> billiardDetails;
  final List<FoodDetail> foodDetails;

  ReportData({
    required this.summary,
    required this.daily,
    required this.billiardDetails,
    required this.foodDetails,
  });

  factory ReportData.fromJson(Map<String, dynamic> json) => ReportData(
        summary: ReportSummary.fromJson(json['summary'] as Map<String, dynamic>),
        daily: (json['daily'] as List<dynamic>)
            .map((e) => DailyCount.fromJson(e as Map<String, dynamic>))
            .toList(),
        billiardDetails: (json['billiardDetails'] as List<dynamic>?)
                ?.map((e) => BilliardDetail.fromJson(e as Map<String, dynamic>))
                .toList() ?? [],
        foodDetails: (json['foodDetails'] as List<dynamic>?)
                ?.map((e) => FoodDetail.fromJson(e as Map<String, dynamic>))
                .toList() ?? [],
      );
}

class ReportSummary {
  final int totalSessions;
  final double dejavuCash;
  final double dejavuTf;
  final double foodCash;
  final double foodTf;
  final double serviceCash;
  final double serviceTf;
  final double glovesCash;
  final double glovesTf;
  final double totalRevenue;
  final int activeBilliardOrders;

  ReportSummary({
    required this.totalSessions,
    required this.dejavuCash,
    required this.dejavuTf,
    required this.foodCash,
    required this.foodTf,
    required this.serviceCash,
    required this.serviceTf,
    required this.glovesCash,
    required this.glovesTf,
    required this.totalRevenue,
    required this.activeBilliardOrders,
  });

  factory ReportSummary.fromJson(Map<String, dynamic> json) => ReportSummary(
        totalSessions: json['totalSessions'] as int,
        dejavuCash: (json['dejavu_cash'] as num).toDouble(),
        dejavuTf: (json['dejavu_tf'] as num).toDouble(),
        foodCash: (json['food_cash'] as num).toDouble(),
        foodTf: (json['food_tf'] as num).toDouble(),
        serviceCash: (json['service_cash'] as num).toDouble(),
        serviceTf: (json['service_tf'] as num).toDouble(),
        glovesCash: (json['gloves_cash'] as num).toDouble(),
        glovesTf: (json['gloves_tf'] as num).toDouble(),
        totalRevenue: (json['totalRevenue'] as num).toDouble(),
        activeBilliardOrders: json['activeBilliardOrders'] as int,
      );
}

class DailyCount {
  final DateTime date;
  final double dejavuCash;
  final double dejavuTf;
  final double foodCash;
  final double foodTf;
  final double serviceCash;
  final double serviceTf;
  final double glovesCash;
  final double glovesTf;
  final double dailyTotal;
  final int dailySessions;

  DailyCount({
    required this.date,
    required this.dejavuCash,
    required this.dejavuTf,
    required this.foodCash,
    required this.foodTf,
    required this.serviceCash,
    required this.serviceTf,
    required this.glovesCash,
    required this.glovesTf,
    required this.dailyTotal,
    required this.dailySessions,
  });

  factory DailyCount.fromJson(Map<String, dynamic> json) => DailyCount(
        date: DateTime.parse(json['date'] as String),
        dejavuCash: (json['dejavu_cash'] as num).toDouble(),
        dejavuTf: (json['dejavu_tf'] as num).toDouble(),
        foodCash: (json['food_cash'] as num).toDouble(),
        foodTf: (json['food_tf'] as num).toDouble(),
        serviceCash: (json['service_cash'] as num).toDouble(),
        serviceTf: (json['service_tf'] as num).toDouble(),
        glovesCash: (json['gloves_cash'] as num).toDouble(),
        glovesTf: (json['gloves_tf'] as num).toDouble(),
        dailyTotal: (json['dailyTotal'] as num).toDouble(),
        dailySessions: json['dailySessions'] as int,
      );
}

class BilliardDetail {
  final int id;
  final String? tableNumber;
  final DateTime? startTime;
  final double? duration;
  final double? price;
  final double? discount;
  final int? status;

  BilliardDetail({
    required this.id,
    this.tableNumber,
    this.startTime,
    this.duration,
    this.price,
    this.discount,
    this.status,
  });

  factory BilliardDetail.fromJson(Map<String, dynamic> json) => BilliardDetail(
        id: json['id'] as int,
        tableNumber: json['tableNumber'] as String?,
        startTime: json['startTime'] != null
            ? DateTime.parse(json['startTime'] as String)
            : null,
        duration: json['duration'] != null
            ? (json['duration'] as num).toDouble()
            : null,
        price: json['price'] != null
            ? (json['price'] as num).toDouble()
            : null,
        discount: json['discount'] != null
            ? (json['discount'] as num).toDouble()
            : null,
        status: json['status'] as int?,
      );
}

class FoodDetail {
  final int id;
  final String? foodName;
  final int? quantity;
  final double? priceEach;
  final double? discountPercent;
  final double? totalAmount;

  FoodDetail({
    required this.id,
    this.foodName,
    this.quantity,
    this.priceEach,
    this.discountPercent,
    this.totalAmount,
  });

  factory FoodDetail.fromJson(Map<String, dynamic> json) => FoodDetail(
        id: json['id'] as int,
        foodName: json['foodName'] as String?,
        quantity: json['quantity'] as int?,
        priceEach: json['priceEach'] != null
            ? (json['priceEach'] as num).toDouble()
            : null,
        discountPercent: json['discountPercent'] != null
            ? (json['discountPercent'] as num).toDouble()
            : null,
        totalAmount: json['totalAmount'] != null
            ? (json['totalAmount'] as num).toDouble()
            : null,
      );
}