import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/holiday_service.dart';
import '../../models/holiday.dart';

class HolidayFormPage extends StatefulWidget {
  final Holiday? holiday;
  const HolidayFormPage(this.holiday);

  @override
  _HolidayFormPageState createState() => _HolidayFormPageState();
}

class _HolidayFormPageState extends State<HolidayFormPage> {
  final _formKey = GlobalKey<FormState>();
  DateTime? _date;
  final _nameCtrl = TextEditingController();
  late final HolidayService _svc;

  @override
  void initState() {
    super.initState();
    if (widget.holiday != null) {
      if (widget.holiday!.holidayDate is DateTime) {
        _date = widget.holiday!.holidayDate as DateTime;
      } else if (widget.holiday!.holidayDate is String) {
        _date = DateTime.tryParse(widget.holiday!.holidayDate as String);
      } else {
        _date = null;
      }
      _nameCtrl.text = widget.holiday!.name;
    }
  }

  @override
  void dispose() {
    _nameCtrl.dispose();
    super.dispose();
  }


  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _svc = context.read<HolidayService>();
  }

  Future<void> _pickDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _date ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null) setState(() => _date = picked);
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate() || _date == null) return;
    final h = Holiday(
      id: widget.holiday?.id ?? 0,
      holidayDate: _date!,
      name: _nameCtrl.text.trim(),
    );
    if (widget.holiday == null) {
      await _svc.create(h);
    } else {
      await _svc.update(h);
    }
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.holiday==null? 'Tambah Hari Besar' : 'Edit Hari Besar')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(children: [
            ListTile(
              title: Text(_date==null
                ? 'Pilih Tanggal'
                : _date!.toLocal().toIso8601String().split('T').first),
              trailing: Icon(Icons.calendar_today),
              onTap: _pickDate,
            ),
            TextFormField(
              controller: _nameCtrl,
              decoration: InputDecoration(labelText: 'Nama Hari Besar'),
              validator: (v)=> v==null||v.isEmpty?'Wajib diisi':null,
            ),
            Spacer(),
            ElevatedButton(
              onPressed: _save,
              child: Text('Simpan'),
            ),
          ]),
        ),
      ),
    );
  }
}
