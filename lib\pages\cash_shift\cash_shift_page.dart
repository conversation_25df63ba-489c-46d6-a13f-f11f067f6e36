import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../data/cash_shift_service.dart';
import '../../models/cash_shift.dart';
import 'start_shift_page.dart';
import 'end_shift_page.dart';
import 'shift_report_page.dart';

class CashShiftPage extends StatefulWidget {
  const CashShiftPage({super.key});

  @override
  State<CashShiftPage> createState() => _CashShiftPageState();
}

class _CashShiftPageState extends State<CashShiftPage> {
  CashShift? _currentShift;
  bool _isLoading = true;
  String? _error;
  String _userName = '';

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    _loadCurrentShift();
  }

  Future<void> _loadUserInfo() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _userName = prefs.getString('full_name') ?? 'User';
    });
  }

  Future<void> _loadCurrentShift() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cashShiftService = context.read<CashShiftService>();
      final shift = await cashShiftService.getCurrentActiveShift();
      setState(() {
        _currentShift = shift;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _startShift() async {
    final result = await Navigator.push<CashShift>(
      context,
      MaterialPageRoute(builder: (_) => const StartShiftPage()),
    );

    if (result != null) {
      setState(() {
        _currentShift = result;
      });
    }
  }

  Future<void> _endShift() async {
    if (_currentShift == null) return;

    final result = await Navigator.push<CashShift>(
      context,
      MaterialPageRoute(
        builder: (_) => EndShiftPage(currentShift: _currentShift!),
      ),
    );

    if (result != null) {
      setState(() {
        _currentShift = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Manajemen Shift Kas'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.assessment),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const ShiftReportPage()),
              );
            },
            tooltip: 'Laporan Shift',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCurrentShift,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.red.shade300),
                      const SizedBox(height: 16),
                      Text('Error: $_error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadCurrentShift,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // User Info Card
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: Colors.green.shade700,
                                child: Text(
                                  _userName.isNotEmpty ? _userName[0].toUpperCase() : 'U',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _userName,
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    'Petugas Kasir',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Current Shift Status
                      Text(
                        'Status Shift Saat Ini',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),

                      if (_currentShift == null)
                        // No Active Shift
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(24),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.schedule,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'Tidak Ada Shift Aktif',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Mulai shift untuk memulai pencatatan transaksi',
                                  style: TextStyle(color: Colors.grey.shade600),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton.icon(
                                    onPressed: _startShift,
                                    icon: const Icon(Icons.play_arrow),
                                    label: const Text('Mulai Shift'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green.shade700,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(vertical: 12),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      else
                        // Active Shift
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade100,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.circle, size: 8, color: Colors.green.shade700),
                                          const SizedBox(width: 4),
                                          Text(
                                            'AKTIF',
                                            style: TextStyle(
                                              color: Colors.green.shade700,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(),
                                    Text(
                                      'Shift #${_currentShift!.id}',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),

                                _buildShiftInfoRow(
                                  'Mulai Shift',
                                  DateFormat('dd/MM/yyyy HH:mm').format(_currentShift!.startTime),
                                  Icons.schedule,
                                ),
                                _buildShiftInfoRow(
                                  'Durasi',
                                  _currentShift!.formattedDuration,
                                  Icons.timer,
                                ),
                                _buildShiftInfoRow(
                                  'Kas Awal',
                                  currencyFormat.format(_currentShift!.startingCash),
                                  Icons.money,
                                ),
                                if (_currentShift!.totalSales != null)
                                  _buildShiftInfoRow(
                                    'Total Penjualan',
                                    currencyFormat.format(_currentShift!.totalSales!),
                                    Icons.attach_money,
                                  ),
                                if (_currentShift!.totalCash != null)
                                  _buildShiftInfoRow(
                                    'Penjualan Cash',
                                    currencyFormat.format(_currentShift!.totalCash!),
                                    Icons.payments,
                                  ),

                                const SizedBox(height: 16),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton.icon(
                                    onPressed: _endShift,
                                    icon: const Icon(Icons.stop),
                                    label: const Text('Akhiri Shift'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.orange.shade700,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(vertical: 12),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                      const SizedBox(height: 24),

                      // Quick Actions
                      Text(
                        'Aksi Cepat',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),

                      Row(
                        children: [
                          Expanded(
                            child: Card(
                              child: InkWell(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(builder: (_) => const ShiftReportPage()),
                                  );
                                },
                                child: const Padding(
                                  padding: EdgeInsets.all(16),
                                  child: Column(
                                    children: [
                                      Icon(Icons.assessment, size: 32, color: Colors.blue),
                                      SizedBox(height: 8),
                                      Text(
                                        'Laporan Shift',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Card(
                              child: InkWell(
                                onTap: _loadCurrentShift,
                                child: const Padding(
                                  padding: EdgeInsets.all(16),
                                  child: Column(
                                    children: [
                                      Icon(Icons.refresh, size: 32, color: Colors.green),
                                      SizedBox(height: 8),
                                      Text(
                                        'Refresh Data',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildShiftInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey.shade600),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(color: Colors.grey.shade600),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
