import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/setting.dart';

class SettingService {
  final String baseUrl;
  SettingService(this.baseUrl);

  Future<List<Setting>> fetchSettings() async {
    final res = await http.get(Uri.parse('$baseUrl/settings'));

    if (res.statusCode == 200) {
      final data = json.decode(res.body) as List;
      return data.map((json) => Setting.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load settings');
    }
  }

  Future<void> updateSetting(int id, String value) async {
    final res = await http.put(
      Uri.parse('$baseUrl/settings'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'id': id, 'value': value}),
    );

    if (res.statusCode != 200) {
      throw Exception('Failed to update setting');
    }
  }

  Future<Setting?> getSettingByName(String name) async {
    final res = await http.get(Uri.parse('$baseUrl/settings'));

    if (res.statusCode == 200) {
      final data = json.decode(res.body) as List;
      final settings = data.map((json) => Setting.fromJson(json)).toList();

      try {
        return settings.firstWhere((s) => s.name == name);
      } catch (e) {
        return null;
      }
    } else {
      throw Exception('Failed to load settings');
    }
  }
}
