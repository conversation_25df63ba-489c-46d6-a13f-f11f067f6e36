import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/cash_shift_service.dart';
import '../../data/user_service.dart';
import '../../models/cash_shift.dart';
import '../../models/user.dart';

class HandoverShiftPage extends StatefulWidget {
  final CashShift currentShift;

  const HandoverShiftPage({
    super.key,
    required this.currentShift,
  });

  @override
  State<HandoverShiftPage> createState() => _HandoverShiftPageState();
}

class _HandoverShiftPageState extends State<HandoverShiftPage> {
  final _formKey = GlobalKey<FormState>();
  final _endingCashController = TextEditingController();
  final _newStartingCashController = TextEditingController();
  final _notesController = TextEditingController();
  
  bool _isLoading = false;
  bool _isLoadingUsers = true;
  List<User> _users = [];
  User? _selectedUser;

  @override
  void initState() {
    super.initState();
    _loadUsers();
    // Set default ending cash to calculated amount
    final calculatedCash = widget.currentShift.calculatedEndingCash;
    _endingCashController.text = NumberFormat('#,###', 'id_ID').format(calculatedCash.round());
    _newStartingCashController.text = NumberFormat('#,###', 'id_ID').format(calculatedCash.round());
  }

  @override
  void dispose() {
    _endingCashController.dispose();
    _newStartingCashController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    try {
      final userService = context.read<UserService>();
      final users = await userService.fetchUser();
      setState(() {
        _users = users.where((user) => user.id != widget.currentShift.userId).toList();
        _isLoadingUsers = false;
      });
    } catch (e) {
      setState(() => _isLoadingUsers = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal memuat daftar user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handoverShift() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pilih user penerima shift'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final cashShiftService = context.read<CashShiftService>();
      final endingCash = double.parse(_endingCashController.text.replaceAll(',', ''));
      final newStartingCash = double.parse(_newStartingCashController.text.replaceAll(',', ''));
      
      final shift = await cashShiftService.handOverShift(
        toUserId: _selectedUser!.id,
        endingCash: endingCash,
        newStartingCash: newStartingCash,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Shift berhasil diserahkan ke ${_selectedUser!.name}'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, shift);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Gagal serah terima shift: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);
    final shift = widget.currentShift;
    final calculatedCash = shift.calculatedEndingCash;
    final actualCash = double.tryParse(_endingCashController.text.replaceAll(',', '')) ?? calculatedCash;
    final difference = actualCash - calculatedCash;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Serah Terima Shift'),
        backgroundColor: Colors.purple.shade700,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.person_remove, color: Colors.purple.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Dari: ${shift.userName}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.access_time, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Durasi Shift: ${shift.formattedDuration}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              Text(
                'Penerima Shift',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              
              if (_isLoadingUsers)
                const Center(child: CircularProgressIndicator())
              else
                DropdownButtonFormField<User>(
                  value: _selectedUser,
                  decoration: InputDecoration(
                    labelText: 'Pilih User Penerima',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: _users.map((user) {
                    return DropdownMenuItem<User>(
                      value: user,
                      child: Text('${user.name} (${user.username})'),
                    );
                  }).toList(),
                  onChanged: (User? value) {
                    setState(() => _selectedUser = value);
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Pilih user penerima shift';
                    }
                    return null;
                  },
                ),
              const SizedBox(height: 16),
              
              Text(
                'Kas Akhir Shift Saat Ini',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _endingCashController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    if (newValue.text.isEmpty) return newValue;
                    final number = int.tryParse(newValue.text);
                    if (number == null) return oldValue;
                    final formatted = NumberFormat('#,###', 'id_ID').format(number);
                    return TextEditingValue(
                      text: formatted,
                      selection: TextSelection.collapsed(offset: formatted.length),
                    );
                  }),
                ],
                decoration: InputDecoration(
                  labelText: 'Kas Akhir Aktual',
                  hintText: 'Hitung kas fisik yang ada',
                  prefixText: 'Rp ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Kas akhir harus diisi';
                  }
                  final number = double.tryParse(value.replaceAll(',', ''));
                  if (number == null || number < 0) {
                    return 'Masukkan jumlah yang valid';
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // Auto-update new starting cash
                    _newStartingCashController.text = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              
              Text(
                'Kas Awal untuk Shift Baru',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _newStartingCashController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    if (newValue.text.isEmpty) return newValue;
                    final number = int.tryParse(newValue.text);
                    if (number == null) return oldValue;
                    final formatted = NumberFormat('#,###', 'id_ID').format(number);
                    return TextEditingValue(
                      text: formatted,
                      selection: TextSelection.collapsed(offset: formatted.length),
                    );
                  }),
                ],
                decoration: InputDecoration(
                  labelText: 'Kas Awal Shift Baru',
                  hintText: 'Kas awal untuk user penerima',
                  prefixText: 'Rp ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Kas awal shift baru harus diisi';
                  }
                  final number = double.tryParse(value.replaceAll(',', ''));
                  if (number == null || number < 0) {
                    return 'Masukkan jumlah yang valid';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Catatan Serah Terima',
                  hintText: 'Catatan tentang kondisi kas, hal penting, dll...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const Spacer(),
              
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _handoverShift,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple.shade700,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Serah Terima Shift',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
