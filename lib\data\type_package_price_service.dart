// lib/data/type_package_price_service.dart

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/type_package_price.dart';

class TypePackagePriceService {
  /// Base URL of the API (e.g. https://your-domain.com)
  final String apiUrl;
  final http.Client _client;

  TypePackagePriceService({
    required this.apiUrl,
    http.Client? client,
  }) : _client = client ?? http.Client();

  /// Fetches the list of TypePackagePrice for a given tableType and packageId
  Future<List<TypePackagePrice>> fetch(
  String tableType,
  int packageId, {
  String? dayType,      // baru: optional
}) async {
  // 1) Buat list query params
  final params = <String, String>{
    'table_type': tableType,
    'package_id': packageId.toString(),
  };
  if (dayType != null) {
    params['day_type'] = dayType;
  }

  // 2) Bangun URI dengan Uri.https agar encoding aman
  final uri = Uri.parse('$apiUrl/type_package_prices').replace(queryParameters: params);

  print('→ GET $uri');
  final response = await _client.get(uri);
  print('← ${response.statusCode}: ${response.body}');

  if (response.statusCode == 200) {
    final List<dynamic> jsonList = json.decode(response.body) as List<dynamic>;
    return jsonList
        .map((e) => TypePackagePrice.fromJson(e as Map<String, dynamic>))
        .toList();
  } else {
    throw Exception(
      'Failed to fetch type package prices (status: ${response.statusCode})',
    );
  }
}


  /// Inserts or updates the list of tiers for the given tableType and packageId
  Future<void> upsert(
    String tableType,
    int packageId,
    List<TypePackagePrice> tiers,
  ) async {
    // match the underscore route
    final uri = Uri.parse('$apiUrl/type_package_prices');

    // snake_case keys to match your controller
    final payload = {
      'table_type': tableType,
      'package_id': packageId,
      'tiers': tiers.map((e) => e.toJson()).toList(),
    };

    print('→ POST $uri');
    print('📨 Payload: ${json.encode(payload)}');
    final response = await _client.post(
      uri,
      headers: {'Content-Type': 'application/json'},
      body: json.encode(payload),
    );
    print('← ${response.statusCode}: ${response.body}');

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception(
        'Failed to upsert type package prices (status: ${response.statusCode})',
      );
    }
  }
}
