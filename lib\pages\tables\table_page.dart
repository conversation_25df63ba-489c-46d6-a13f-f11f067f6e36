// lib/features/pages/dashboard/table_master_page.dart
// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/table_service.dart';
import '../../models/table.dart';
import 'table_form.dart';

class TableMasterPage extends StatefulWidget {
  const TableMasterPage({super.key});

  @override
  State<TableMasterPage> createState() => _TableMasterPageState();
}

class _TableMasterPageState extends State<TableMasterPage> {
  late final TableApiService apiService;

  bool _initialized = false;

  String searchQuery = '';
  int? selectedStatus;

  List<BilliardTable> _allTables = [];
  List<BilliardTable> _filteredTables = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      apiService = context.read<TableApiService>();
      _loadTables();
      _initialized = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: AppBar(
        backgroundColor: Colors.green.shade700,
        elevation: 4,
        title: const Text(
          'Master Data Meja',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'Refresh Data',
            onPressed: _loadTables,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    onChanged: (value) {
                      setState(() {
                        searchQuery = value;
                        _applyFilters();
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Cari Meja...',
                      prefixIcon: const Icon(Icons.search),
                      contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: DropdownButtonFormField<int>(
                    value: selectedStatus,
                    hint: const Text('Filter Status'),
                    items: const [
                      DropdownMenuItem<int>(value: null, child: Text('Semua')),
                      DropdownMenuItem<int>(value: 0, child: Text('Tersedia')),
                      DropdownMenuItem<int>(value: 1, child: Text('Dipakai')),
                      DropdownMenuItem<int>(value: 2, child: Text('Maintenance')),
                    ],
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    onChanged: (value) {
                      setState(() {
                        selectedStatus = value;
                        _applyFilters();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: () => _openForm(),
                    icon: const Icon(Icons.add, color: Colors.white),
                    label: const Text('Tambah', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 18),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return _filteredTables.isEmpty
                      ? const Center(child: Text('Belum ada meja.'))
                      : GridView.builder(
                          itemCount: _filteredTables.length,
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            childAspectRatio: 1.8,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                          ),
                          itemBuilder: (context, index) {
                            final table = _filteredTables[index];
                            return _buildTableCard(table);
                          },
                        );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _applyFilters() {
    setState(() {
      _filteredTables = _allTables.where((table) {
        final matchesSearch = table.tableNumber.toLowerCase().contains(searchQuery.toLowerCase());
        final matchesStatus = selectedStatus == null || table.status == selectedStatus;
        return matchesSearch && matchesStatus;
      }).toList();
    });
  }

  Widget _buildStatusBadge(int status) {
    late final String label;
    late final Color color;
    switch (status) {
      case 0:
        label = 'Tersedia';
        color = Colors.green;
        break;
      case 1:
        label = 'Dipakai';
        color = Colors.red;
        break;
      case 2:
        label = 'Maintenance';
        color = Colors.orange;
        break;
      default:
        label = 'Unknown';
        color = Colors.grey;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(6)),
      child: Text(label, style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildTypeBadge(String type) {
    late final Color color;
    switch (type) {
      case 'VVIP':
        color = Colors.purple;
        break;
      case 'PREMIUM':
        color = Colors.blue;
        break;
      case 'PRESIDENT':
        color = Colors.orange;
        break;
      case 'EXCLUSIVE':
        color = Colors.teal;
        break;
      default:
        color = Colors.green;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(6)),
      child: Text(type, style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildTableCard(BilliardTable table) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.green.shade800, Colors.amber.shade800],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.table_chart, color: Colors.white, size: 20),
                    const SizedBox(width: 6),
                    Text(
                      'Meja ${table.tableNumber}',
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18, color: Colors.white),
                    ),
                  ],
                ),
                Row(
                  children: [
                    _buildStatusBadge(table.status),
                    const SizedBox(width: 6),
                    _buildTypeBadge(table.type),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Harga:', style: TextStyle(color: Colors.white, fontSize: 14)),
                Text(
                  NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(table.price),
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
            const Divider(color: Colors.white30, thickness: 1),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openForm(table: table),
                    icon: const Icon(Icons.edit, size: 18, color: Colors.white),
                    label: const Text('Edit', style: TextStyle(fontSize: 14, color: Colors.white)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _confirmDelete(table.id, table.tableNumber),
                    icon: const Icon(Icons.delete, size: 18, color: Colors.white),
                    label: const Text('Hapus', style: TextStyle(fontSize: 14, color: Colors.white)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _confirmDelete(int id, String tableNumber) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Konfirmasi Hapus'),
        content: Text('Apakah kamu yakin ingin menghapus Meja $tableNumber?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Hapus', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      _deleteTable(id);
    }
  }

  Future<void> _deleteTable(int id) async {
    try {
      await apiService.deleteTable(id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Meja berhasil dihapus')),
      );
      _loadTables();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal menghapus meja: $e')),
      );
    }
  }

  Future<void> _loadTables() async {
    try {
      final tables = await apiService.fetchTables();
      setState(() {
        _allTables = tables;
        _applyFilters();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal memuat data meja: $e')),
      );
    }
  }

  Future<void> _openForm({BilliardTable? table}) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => TableFormPage(table: table)),
    );
    if (result == true) {
      _loadTables();
    }
  }
}
