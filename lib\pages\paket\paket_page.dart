// lib/features/pages/packages/paket_page.dart

// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../data/paket_service.dart';
import '../../models/paket.dart';
import 'paket_form.dart';

class PaketPage extends StatefulWidget {
  const PaketPage({super.key});
  @override
  State<PaketPage> createState() => _PaketPageState();
}

class _PaketPageState extends State<PaketPage> {
  late final RentalPackageService apiService;
  bool _init = false;
  List<RentalPackage> allPackages = [];
  List<RentalPackage> filtered = [];
  String search = '';

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_init) {
      apiService = context.read<RentalPackageService>();
      _load();
      _init = true;
    }
  }

  Future<void> _load() async {
    allPackages = await apiService.getAllPackages();
    _apply();
  }

  void _apply() {
    filtered = allPackages.where((p) => p.namaPaket.toLowerCase().contains(search.toLowerCase())).toList();
    setState(() {});
  }

  String fmt(double v) => NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(v);

  @override
  Widget build(BuildContext c) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Master Data Paket',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
        ),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _load, color: Colors.white),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(children: [
          Row(children: [
            Expanded(
              child: TextField(
                decoration: const InputDecoration(hintText: 'Cari Paket…', prefixIcon: Icon(Icons.search), border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8)))),
                onChanged: (v) {
                  search = v;
                  _apply();
                },
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: () => _openForm(),
              icon: const Icon(Icons.add),
              label: const Text('Tambah'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green[700], foregroundColor: Colors.white),
            ),
          ]),
          const SizedBox(height: 10),
          Expanded(
            child: filtered.isEmpty
                ? const Center(child: Text('Belum ada paket.'))
                : GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4, childAspectRatio: 1.8, crossAxisSpacing: 8, mainAxisSpacing: 8),
                    itemCount: filtered.length,
                    itemBuilder: (_, i) => _card(filtered[i]),
                  ),
          )
        ]),
      ),
    );
  }

  Widget _card(RentalPackage p) {
    // show all tiers in subtitle
    final tierText = p.harga.map((t) => '${t.duration}h: ${fmt(t.price)}').join(' · ');
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.green.shade800, Colors.amber.shade800],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(p.namaPaket, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16)),
            const SizedBox(height: 4),
            Text(tierText, style: const TextStyle(color: Colors.white70, fontSize: 12)),
            const Spacer(),
            Row(children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _openForm(package: p),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, minimumSize: const Size(0, 36), foregroundColor: Colors.white),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _confirmDelete(p.id, p.namaPaket),
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('Hapus'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red, minimumSize: const Size(0, 36), foregroundColor: Colors.white),
                ),
              ),
            ])
          ],
        ),
      ),
    );
  }

  Future<void> _openForm({RentalPackage? package}) async {
    final dirty = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => PackageFormPage(package: package)),
    );
    if (dirty == true) _load();
  }

  Future<void> _confirmDelete(int id, String name) async {
    final ok = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Hapus Paket'),
        content: Text('Yakin hapus "$name"?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
          ElevatedButton(style: ElevatedButton.styleFrom(backgroundColor: Colors.red), onPressed: () => Navigator.pop(context, true), child: const Text('Hapus')),
        ],
      ),
    );
    if (ok == true) {
      try {
        await apiService.deletePackage(id);
        _load();
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Paket dihapus')));
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Gagal hapus: $e')));
      }
    }
  }
}
