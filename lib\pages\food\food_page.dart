// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/menu_item_service.dart';
import '../../models/food.dart';
import 'food_form_page.dart';

class MenuItemsPage extends StatefulWidget {
  const MenuItemsPage({super.key});

  @override
  State<MenuItemsPage> createState() => _MenuItemsPageState();
}

class _MenuItemsPageState extends State<MenuItemsPage> {
  late final FoodService apiService;
  bool _initialized = false;

  String searchQuery = '';
  String selectedCategory = 'Semua';
  final List<String> _categories = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>uman <PERSON>',
    'Minuman Dingin',
  ];

  List<Food> _items = [];
  List<Food> _filteredItems = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: AppBar(
        backgroundColor: Colors.green.shade700,
        elevation: 4,
        title: const Text('Menu Items', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18)),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'Refresh Data',
            onPressed: _loadItems,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    onChanged: (value) {
                      setState(() {
                        searchQuery = value;
                        _applyFilters();
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Cari menu...',
                      isDense: true,
                      prefixIcon: const Icon(Icons.search),
                      contentPadding: const EdgeInsets.all(8),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  flex: 2,
                  child: DropdownButtonFormField<String>(
                    value: selectedCategory,
                    items: _categories
                        .map(
                          (cat) => DropdownMenuItem(
                            value: cat,
                            child: Text(cat),
                          ),
                        )
                        .toList(),
                    decoration: InputDecoration(
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      filled: true,
                      fillColor: Colors.white,
                      isDense: true,
                    ),
                    onChanged: (val) {
                      if (val != null) {
                        setState(() {
                          selectedCategory = val;
                          _applyFilters();
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 10),
                ElevatedButton.icon(
                  onPressed: () => _goToForm(),
                  icon: const Icon(Icons.add, color: Colors.white),
                  label: const Text('Tambah', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[700],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Expanded(
              child: _filteredItems.isEmpty
                  ? Center(child: Text('Belum ada item menu.'))
                  : ListView.builder(
                      itemCount: _filteredItems.length,
                      itemBuilder: (_, i) {
                        final m = _filteredItems[i];
                        return Card(
                          color: Colors.white,
                          borderOnForeground: true,
                          shadowColor: Colors.black26,
                          child: ListTile(
                            title: Row(
                              children: [
                                Text(m.name, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                const SizedBox(width: 10),
                                Badge(
                                  label: Text(m.isAvailable ? 'Tersedia' : 'Tidak tersedia', style: const TextStyle(color: Colors.white)),
                                  backgroundColor: m.isAvailable ? Colors.green : Colors.grey,
                                ),
                              ],
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text('Kategori: ', style: const TextStyle(fontWeight: FontWeight.bold)),
                                    Text(m.category),
                                  ],
                                ),
                                const SizedBox(height: 3),
                                Row(
                                  children: [
                                    Text('Harga: ', style: const TextStyle(fontWeight: FontWeight.bold)),
                                    Text(NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(m.price)),
                                  ],
                                ),
                              ],
                            ),
                            onTap: () => _goToForm(m),
                            trailing: ElevatedButton.icon(
                              onPressed: () => _confirmDelete(m.id!, m.name),
                              label: const Icon(Icons.delete, size: 18, color: Colors.white),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (!_initialized) {
      apiService = context.read<FoodService>();
      _loadItems();
      _initialized = true;
    }
  }

  @override
  void initState() {
    super.initState();
  }

  void _applyFilters() {
    setState(() {
      _filteredItems = _items.where((item) {
        final matchesSearch = item.name.toLowerCase().contains(searchQuery.toLowerCase()) || item.price.toString().toLowerCase().contains(searchQuery.toLowerCase());
        final matchesCategory = selectedCategory == 'Semua' || item.category == selectedCategory;
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  Future<void> _confirmDelete(int id, String name) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Konfirmasi Hapus'),
        content: Text('Apakah kamu yakin ingin menghapus Menu $name?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Hapus', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      _deleteItem(id);
    }
  }

  void _deleteItem(int id) async {
    try {
      await apiService.deleteFood(id);

      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Menu berhasil dihapus')));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Gagal menghapus menu: $e')));
    }
  }

  void _goToForm([Food? item]) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => MenuItemFormPage(menuItem: item)),
    );
    if (result == true) _loadItems();
  }

  Future<void> _loadItems() async {
    try {
      final items = await apiService.fetchFoods();

      setState(() {
        _items = items;
        _applyFilters();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Gagal memuat data menu: $e')));
    }
  }
}
