// lib/screens/home_screen.dart

import 'dart:async';
import 'dart:io';
import 'package:dart_ping/dart_ping.dart';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../data/relay_service.dart';
import '../models/relay.dart';

import '../pages/dashboard_page.dart';
import '../pages/sessions/sessions_page.dart';
import '../pages/type_prices/type_price_page.dart';
import '../pages/tables/table_page.dart';
import '../pages/food/food_page.dart';
import '../pages/reports_page.dart';
import '../pages/settings/settings_page.dart';
import '../pages/relay/relay_page.dart';
import '../pages/holidays/holiday_list_page.dart'; // <-- new
import '../pages/cash_shift/cash_shift_page.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late Timer _timer;
  Timer? _pingTimer;
  DateTime _now = DateTime.now();

  // relay data
  late final RelayService _relayService;
  List<Relay> _relays = [];
  bool _relaysInitialized = false;
  Map<String, bool> _ipStatus = {};

  final List<Widget> _pages = [
    BilliardDashboard(),
    SessionsPage(),
    CashShiftPage(),
    TypePricePage(),
    TableMasterPage(),
    MenuItemsPage(),
    ReportsPage(),
    SettingsPage(),
    RelayPage(),
    HolidayListPage(), // <-- new Holidays tab
  ];

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() => _now = DateTime.now());
    });
    // every 10s, update all IP statuses
    _pingTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      _updateIpStatuses();
    });
  }

  /// Returns true if we got at least one response in ~2s.
  Future<bool> _pingIp(String ip) async {
    try {
      // timeout is in milliseconds (2000 ms = 2 s)
      final pinger = Ping(ip, count: 1, timeout: 2000);
      await for (final data in pinger.stream) {
        if (data.response != null) {
          return true;
        }
        if (data.error != null) {
          return false;
        }
      }
      return false;
    } catch (_) {
      return false;
    }
  }

  Future<void> _updateIpStatuses() async {
    final ips = _relays.map((r) => r.ipAddress).whereType<String>().toSet();
    final Map<String, bool> statuses = {};
    for (final ip in ips) {
      statuses[ip] = await _pingIp(ip);
    }
    if (!mounted) return;
    setState(() => _ipStatus = statuses);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_relaysInitialized) {
      _relayService = context.read<RelayService>();
      _fetchRelays();
      _relaysInitialized = true;
    }
  }

  Future<void> _fetchRelays() async {
    final relays = await _relayService.getAllRelays();
    if (!mounted) return;
    setState(() => _relays = relays);
    // immediately populate statuses:
    _updateIpStatuses();
  }

  @override
  void dispose() {
    _timer.cancel();
    _pingTimer?.cancel();
    super.dispose();
  }

  String get _formattedDateTime => DateFormat('EEEE, dd - MMM - yyyy, HH:mm:ss').format(_now);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _pages[_currentIndex],
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.green.shade700,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: ClipRRect(
              child: BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                backgroundColor: Colors.green.shade700,
                currentIndex: _currentIndex,
                selectedItemColor: Colors.white,
                unselectedItemColor: Colors.white70,
                onTap: (index) => setState(() => _currentIndex = index),
                items: const [
                  BottomNavigationBarItem(
                    icon: Icon(Icons.dashboard),
                    label: 'Dashboard',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.receipt_long),
                    label: 'Sessions',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.schedule),
                    label: 'Shift Kas',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.price_check),
                    label: 'Tipe Harga',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.table_bar),
                    label: 'Table',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.restaurant_menu),
                    label: 'Food Menu',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.bar_chart),
                    label: 'Reports',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.settings),
                    label: 'Settings',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.toggle_on),
                    label: 'Relay',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.calendar_today),
                    label: 'Holidays', // <-- new tab
                  ),
                ],
              ),
            ),
          ),
          // ← NEW footer with IPs on left, clock on right
          // inside build()
          Container(
            width: double.infinity,
            color: Colors.blue,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            child: Row(
              children: [
                Expanded(
                  child: Builder(builder: (_) {
                    final seen = <String>{};
                    final widgets = <Widget>[];
                    for (final r in _relays) {
                      final ip = r.ipAddress ?? '';
                      if (ip.isEmpty) continue;
                      if (seen.add(ip)) {
                        final hw = r.hardwareId ?? '';
                        final label = '$ip${hw.isNotEmpty ? ' (HW#$hw)' : ''}';
                        final ok = _ipStatus[ip] ?? false;
                        widgets.add(Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.circle, size: 10, color: ok ? Colors.greenAccent : Colors.redAccent),
                            const SizedBox(width: 4),
                            Text(label, style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600)),
                          ],
                        ));
                      }
                      if (widgets.length >= 3) break;
                    }
                    // if you want separators:
                    return Row(
                      children: [
                        for (var i = 0; i < widgets.length; i++) ...[
                          widgets[i],
                          if (i + 1 < widgets.length)
                            const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8),
                              child: Text('|', style: TextStyle(color: Colors.white70)),
                            ),
                        ]
                      ],
                    );
                  }),
                ),
                Text(_formattedDateTime, style: const TextStyle(fontSize: 18, color: Colors.yellow, fontWeight: FontWeight.w900)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
