class RentalShift {
  final int id;
  final String namaShift;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RentalShift({
    required this.id,
    required this.namaShift,
    this.createdAt,
    this.updatedAt,
  });

  // ✅ Factory method untuk membuat objek dari JSON
  factory RentalShift.fromJson(Map<String, dynamic> json) {
    return RentalShift(
      id: json['id'],
      namaShift: json['nama_shift'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  // ✅ Konversi objek ke JSON (untuk dikirim ke backend)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nama_shift': namaShift,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
