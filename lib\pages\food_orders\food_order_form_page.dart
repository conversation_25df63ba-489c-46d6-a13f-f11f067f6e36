// ignore_for_file: use_build_context_synchronously

import 'package:bilyard_flutter/pages/food_orders/print_food_order_page.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/food_order_service.dart';
import '../../data/menu_item_service.dart';
import '../../data/table_service.dart';
import '../../models/table.dart';
import '../../models/food_order.dart';
import '../../models/food_order_item.dart';
import '../../models/food.dart';

class FoodOrderFormPage extends StatefulWidget {
  final FoodOrder? order;
  const FoodOrderFormPage({super.key, this.order});

  @override
  State<FoodOrderFormPage> createState() => _FoodOrderFormPageState();
}

class _FoodOrderFormPageState extends State<FoodOrderFormPage> {
  final _formKey = GlobalKey<FormState>();
  late final FoodOrderService foodOrderService;

  late final FoodService menuService;
  late final TableApiService tableService;

  int? selectedTableId;
  final TextEditingController _guestNameCtrl = TextEditingController();
  String _status = '0';

  List<BilliardTable> _tables = [];
  List<Food> _menuItems = [];
  List<FoodOrderItem> _items = [];
  bool _loading = true;
  int _currentBatch = 1;

  bool get isEdit => widget.order != null;
  double get _total => _items.fold(0.0, (sum, e) => sum + e.subtotal);

  @override
  Widget build(BuildContext context) {
    final batches = _items.map((e) => e.batch).toSet().toList()..sort();

    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? 'Edit Food Order' : 'Tambah Food Order', style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    DropdownButtonFormField<int>(
                      value: selectedTableId,
                      hint: const Text('Pilih Meja'),
                      decoration: const InputDecoration(
                        labelText: 'Meja',
                        border: OutlineInputBorder(),
                      ),
                      items: _tables
                          .map((t) => DropdownMenuItem(
                                value: t.id,
                                child: Text('Meja ${t.tableNumber}'),
                              ))
                          .toList(),
                      onChanged: (v) => setState(() => selectedTableId = v),
                      validator: (v) => v == null ? 'Silahkan pilih meja' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _guestNameCtrl,
                      decoration: const InputDecoration(
                        labelText: 'Guest Name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v!.isEmpty ? 'Silahkan isi nama tamu' : null,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _status,
                      decoration: const InputDecoration(
                        labelText: 'Status',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: '0', child: Text('Pending')),
                        DropdownMenuItem(value: '1', child: Text('Preparing')),
                        DropdownMenuItem(value: '2', child: Text('Served')),
                        DropdownMenuItem(value: '3', child: Text('Paid')),
                      ],
                      onChanged: (v) => setState(() => _status = v!),
                    ),
                    const Divider(height: 32),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            for (var batch in batches) ...[
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                  child: Text(
                                    'Batch #$batch',
                                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              LayoutBuilder(
                                builder: (context, constraints) {
                                  return SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(minWidth: constraints.maxWidth),
                                      child: DataTable(
                                        columnSpacing: 32,
                                        columns: const [
                                          DataColumn(label: Text('Description')),
                                          DataColumn(label: Text('Qty')),
                                          DataColumn(label: Text('Price')),
                                          DataColumn(label: Text('Subtotal')),
                                          DataColumn(label: Text('Action')),
                                        ],
                                        rows: _items.where((e) => e.batch == batch).map((e) {
                                          final canDelete = e.batch == _currentBatch && e.id == 0;
                                          return DataRow(cells: [
                                            DataCell(Text(e.menuItem['name'])),
                                            DataCell(Text(e.quantity.toString())),
                                            DataCell(Text(NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(e.priceEach))),
                                            DataCell(Text(NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(e.subtotal))),
                                            DataCell(
                                              canDelete
                                                  ? IconButton(
                                                      icon: const Icon(Icons.delete, color: Colors.red),
                                                      onPressed: () {
                                                        final idx = _items.indexOf(e);
                                                        _removeItem(idx);
                                                      },
                                                    )
                                                  : const SizedBox.shrink(),
                                            ),
                                          ]);
                                        }).toList(),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              Align(
                                alignment: Alignment.centerRight,
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 4, bottom: 16),
                                  child: Text(
                                    'Subtotal Batch #$batch: ${NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(_items.where((e) => e.batch == batch).fold(0.0, (sum, e) => sum + e.subtotal))}',
                                    style: const TextStyle(fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Total: ${NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(_total)}',
                            style: const TextStyle(fontSize: 18),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _addItem,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Item'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 18),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _save,
                            icon: Icon(isEdit ? Icons.save : Icons.add),
                            label: Text(
                              isEdit ? 'Simpan Perubahan' : 'Tambah Food Order',
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: isEdit
                                ? () => Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (_) => PrintFoodOrderPage(foodOrderId: widget.order!.id),
                                      ),
                                    )
                                : null,
                            icon: const Icon(Icons.print),
                            label: const Text(
                              'Print Invoice',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _items.isEmpty ? null : _sendToKitchen,
                            icon: const Icon(Icons.send),
                            label: const Text(
                              'Send to Kitchen',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.amber,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    foodOrderService = context.read<FoodOrderService>();
    menuService = context.read<FoodService>();
    tableService = context.read<TableApiService>();

    _loadData();
  }

  @override
  void initState() {
    super.initState();

    _guestNameCtrl.text = widget.order?.guestName ?? '';

    if (isEdit) selectedTableId = widget.order!.table['id'];
  }

  Future<void> _addItem() async {
    Food? chosen;
    int qty = 1;

    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Add Menu Item'),
        content: StatefulBuilder(
          builder: (ctx, setStateDlg) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<Food>(
                isExpanded: true,
                hint: const Text('Select Menu Item'),
                value: chosen,
                items: _menuItems
                    .map((m) => DropdownMenuItem(
                          value: m,
                          child: Text(
                            '${m.name} – ${NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(m.price)}',
                          ),
                        ))
                    .toList(),
                onChanged: (v) => setStateDlg(() => chosen = v),
              ),
              TextFormField(
                initialValue: '1',
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(labelText: 'Quantity'),
                onChanged: (v) {
                  qty = int.tryParse(v) ?? 1;
                  if (qty < 1) qty = 1;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);

              setState(() {
                final batchForThis = isEdit ? _currentBatch : 1;
                final idx = _items.indexWhere((it) => it.menuItem['id'] == chosen!.id && it.batch == batchForThis);

                if (idx != -1) {
                  _items[idx].quantity += qty;
                  _items[idx].subtotal = _items[idx].quantity * _items[idx].priceEach;
                } else {
                  _items.add(FoodOrderItem(
                    id: 0,
                    foodOrderId: widget.order?.id ?? 0,
                    menuItem: {'id': chosen!.id, 'name': chosen!.name, 'price': chosen!.price},
                    quantity: qty,
                    priceEach: chosen!.price,
                    subtotal: qty * chosen!.price,
                    batch: _currentBatch,
                  ));
                }
              });
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  String _convertStatusFromBackend(String s) {
    switch (s) {
      case 'pending':
        return '0';
      case 'preparing':
        return '1';
      case 'served':
        return '2';
      case 'paid':
        return '3';
      default:
        return '0';
    }
  }

  Future<void> _loadData() async {
    setState(() => _loading = true);

    final tables = await tableService.fetchTables();
    final menuItems = await menuService.fetchFoods();

    setState(() {
      if (isEdit) {
        _tables = tables.where((t) => t.id == selectedTableId).toList();
      } else {
        _tables = tables.where((t) => t.status != 0).toList();
      }
      _menuItems = menuItems;
    });

    if (isEdit) {
      final fetched = await foodOrderService.fetchFoodOrderById(widget.order!.id);
      final items = await foodOrderService.fetchFoodOrderItems(widget.order!.id);

      _status = _convertStatusFromBackend(fetched!.status);
      _items = items;

      final maxBatch = _items.map((e) => e.batch).fold<int>(0, (p, b) => b > p ? b : p);

      setState(() {
        _currentBatch = maxBatch + 1;
      });
    }

    setState(() => _loading = false);
  }

  Future<void> _removeItem(int idx) async {
    if (_items[idx].id != 0) {
      await foodOrderService.deleteFoodOrderItem(_items[idx].id);
    }

    setState(() => _items.removeAt(idx));
  }

  Future<bool> _save({bool auto = false}) async {
    if (!_formKey.currentState!.validate()) return false;

    _formKey.currentState!.save();

    String statusToSend;

    switch (_status) {
      case '0':
        statusToSend = '1';
        break;
      case '1':
        statusToSend = '2';
        break;
      case '2':
        statusToSend = '3';
        break;
      default:
        statusToSend = '4';
        break;
    }

    // ─── AFTER ─────────────────────────────────────────────────
    final order = FoodOrder(
      id: widget.order?.id ?? 0,
      table: {'id': selectedTableId!},
      guestName: _guestNameCtrl.text,
      orderTime: DateTime.now(),
      status: statusToSend,
      totalAmount: _total,
      items: _items, // ← pass your current list of items here
    );

    bool ok;

    if (isEdit) {
      ok = await foodOrderService.updateFoodOrder(order.id, order, []);

      if (!ok) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Gagal update order')));
        return false;
      }

      for (var item in _items.where((e) => e.id == 0)) {
        final added = await foodOrderService.addFoodOrderItem(item);

        if (!added) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Gagal tambah item ${item.menuItem['name']}')),
          );
        }
      }
    } else {
      ok = await foodOrderService.createFoodOrder(order, _items);
    }

    if (!auto && ok) Navigator.pop(context, true);

    return ok;
  }

  Future<void> _sendToKitchen() async {
    final batches = _items.map((e) => e.batch).toSet().toList()..sort();

    if (batches.isEmpty || widget.order == null) return;

    int? selectedBatch;

    if (batches.length == 1) {
      selectedBatch = batches.first;
    } else {
      selectedBatch = await showDialog<int>(
        context: context,
        barrierDismissible: true,
        builder: (_) {
          int temp = batches.last;
          return StatefulBuilder(
            builder: (ctx, setStateDlg) => AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              title: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 30),
                  const SizedBox(width: 10),
                  const Text('Pilih Batch untuk Dikirim'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: batches
                    .map((b) => RadioListTile<int>(
                          title: Text('Batch $b'),
                          value: b,
                          groupValue: temp,
                          onChanged: (v) => setStateDlg(() => temp = v!),
                        ))
                    .toList(),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, null),
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Batal'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, temp),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Lanjutkan'),
                ),
              ],
            ),
          );
        },
      );

      if (selectedBatch == null) return;
    }

    final saved = await _save(auto: true);
    if (!saved) return;

    try {
      await foodOrderService.sendToKitchen(widget.order!.id);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Batch dikirim ke kitchen')),
      );

      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (_) => SendToKitchenPage(
      //       foodOrderId: widget.order!.id,
      //       batch: selectedBatch!,
      //     ),
      //   ),
      // );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal kirim ke kitchen: $e')),
      );
    }
  }
}
