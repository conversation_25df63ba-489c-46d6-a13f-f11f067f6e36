// lib/data/food_order_service.dart
import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/food_order.dart';
import '../models/food_order_item.dart';

class FoodOrderService {
  final String baseUrl;
  FoodOrderService(this.baseUrl);

  Future<String?> _token() async => (await SharedPreferences.getInstance()).getString('token');

  Future<Map<String, String>> _headers() async {
    final t = await _token();
    return {
      'Content-Type': 'application/json',
      if (t != null) 'Authorization': 'Bearer $t',
    };
  }

  Future<List<FoodOrder>> fetchFoodOrders() async {
    final uri = Uri.parse('$baseUrl/food_orders');
    final resp = await http.get(uri, headers: await _headers());
    if (resp.statusCode == 200) {
      final data = jsonDecode(resp.body) as List;
      return data.map((e) => FoodOrder.fromJson(e)).toList();
    }
    throw Exception('Failed to fetch food orders');
  }

  Future<FoodOrder?> fetchFoodOrderById(int id) async {
    final uri = Uri.parse('$baseUrl/food_orders/$id');
    final resp = await http.get(uri, headers: await _headers());
    if (resp.statusCode == 200) {
      return FoodOrder.fromJson(jsonDecode(resp.body));
    }
    return null;
  }

  Future<bool> createFoodOrder(FoodOrder order, List<FoodOrderItem> items) async {
    final uri = Uri.parse('$baseUrl/food_orders');
    final resp = await http.post(
      uri,
      headers: await _headers(),
      body: jsonEncode({
        'order': order.toJson(),
        'items': items.map((e) => e.toJson()).toList(),
      }),
    );

    return resp.statusCode == 201;
  }

  Future<bool> updateFoodOrder(int id, FoodOrder order, List<FoodOrderItem> items) async {
    final uri = Uri.parse('$baseUrl/food_orders/$id');
    final resp = await http.put(
      uri,
      headers: await _headers(),
      body: jsonEncode({
        'order': order.toJson(),
        'items': items.map((e) => e.toJson()).toList(),
      }),
    );
    return resp.statusCode == 200;
  }

  Future<bool> deleteFoodOrder(int id) async {
    final uri = Uri.parse('$baseUrl/food_orders/$id');
    final resp = await http.delete(uri, headers: await _headers());
    return resp.statusCode == 200;
  }

  Future<List<FoodOrderItem>> fetchFoodOrderItems(int orderId) async {
    final uri = Uri.parse('$baseUrl/food_orders/$orderId/items');
    final resp = await http.get(uri, headers: await _headers());
    if (resp.statusCode == 200) {
      final data = jsonDecode(resp.body) as List;
      return data.map((e) => FoodOrderItem.fromJson(e)).toList();
    }
    throw Exception('Failed to fetch order items');
  }

  Future<bool> addFoodOrderItem(FoodOrderItem item) async {
    final uri = Uri.parse('$baseUrl/food_orders/${item.foodOrderId}/items');
    final resp = await http.post(
      uri,
      headers: await _headers(),
      body: jsonEncode({
        'menu_item_id': item.menuItem['id'],
        'quantity': item.quantity,
        'price_each': item.priceEach,
      }),
    );
    return resp.statusCode == 201;
  }

  Future<bool> deleteFoodOrderItem(int itemId) async {
    final uri = Uri.parse('$baseUrl/food_order_items/$itemId');
    final resp = await http.delete(uri, headers: await _headers());
    return resp.statusCode == 200;
  }

  Future<bool> sendToKitchen(int orderId) async {
    final uri = Uri.parse('$baseUrl/food_orders/$orderId');
    final payload = {
      'order': {
        'status': 'preparing',
      },
    };
    final resp = await http.patch(
      uri,
      headers: await _headers(),
      body: jsonEncode(payload),
    );
    return resp.statusCode == 200;
  }
}
