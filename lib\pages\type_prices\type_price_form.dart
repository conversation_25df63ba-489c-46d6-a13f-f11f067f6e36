// lib/features/pages/packages/type_price_form.dart

// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../data/type_package_price_service.dart';
import '../../../data/paket_service.dart';
import '../../../models/type_package_price.dart';
import '../../../models/paket.dart';

class TypePriceFormPage extends StatefulWidget {
  final String tableType;
  const TypePriceFormPage({super.key, required this.tableType});

  @override
  State<TypePriceFormPage> createState() => _TypePriceFormPageState();
}

class _TypePriceFormPageState extends State<TypePriceFormPage> {
  final _formKey = GlobalKey<FormState>();
  late final TypePackagePriceService _typeSvc;
  late final RentalPackageService _pkgSvc;

  bool _loading = true;
  List<RentalPackage> _packages = [];
  RentalPackage? _pkg;
  List<TypePackagePrice> _tiers = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _typeSvc = context.read<TypePackagePriceService>();
    _pkgSvc = context.read<RentalPackageService>();
    _init();
  }

  Future<void> _init() async {
    final pkgs = await _pkgSvc.getAllPackages();
    setState(() {
      _packages = pkgs;
      _pkg = pkgs.isNotEmpty ? pkgs.first : null;
    });
    await _loadTiers();
    setState(() => _loading = false);
  }

  Future<void> _loadTiers() async {
    if (_pkg == null) return;
    final tiers = await _typeSvc.fetch(widget.tableType, _pkg!.id);
    setState(() => _tiers = tiers);
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate() || _pkg == null) return;
    setState(() => _loading = true);
    await _typeSvc.upsert(widget.tableType, _pkg!.id, _tiers);
    Navigator.pop(context, true);
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Harga ${widget.tableType}'),
        backgroundColor: Colors.green[700],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // — Package dropdown —
              DropdownButtonFormField<RentalPackage>(
                decoration: const InputDecoration(
                  labelText: 'Pilih Paket',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.card_membership),
                ),
                value: _pkg,
                items: _packages
                    .map((p) => DropdownMenuItem(
                          value: p,
                          child: Text(p.namaPaket),
                        ))
                    .toList(),
                onChanged: (v) {
                  setState(() {
                    _pkg = v;
                    _loading = true;
                  });
                  _loadTiers().then((_) => setState(() => _loading = false));
                },
                validator: (v) => v == null ? 'Paket wajib dipilih' : null,
              ),
              const SizedBox(height: 16),

              // — Editable tiers list —
              Expanded(
                child: ListView(
                  children: [
                    for (var i = 0; i < _tiers.length; i++)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        child: Row(
                          children: [
                            // Duration (jam)
                            Expanded(
                              child: TextFormField(
                                initialValue: '${_tiers[i].duration}',
                                decoration: const InputDecoration(
                                  labelText: 'Durasi (jam)',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.access_time),
                                ),
                                keyboardType: TextInputType.number,
                                validator: (v) => (int.tryParse(v ?? '') == null)
                                    ? 'Masukkan angka bulat'
                                    : null,
                                onChanged: (v) => setState(() {
                                  _tiers[i].duration =
                                      int.tryParse(v) ?? _tiers[i].duration;
                                }),
                              ),
                            ),
                            const SizedBox(width: 8),

                            // Harga
                            Expanded(
                              child: TextFormField(
                                initialValue: _tiers[i].price.toStringAsFixed(0),
                                decoration: const InputDecoration(
                                  labelText: 'Harga',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.attach_money),
                                ),
                                keyboardType: TextInputType.number,
                                validator: (v) => (double.tryParse(v ?? '') == null)
                                    ? 'Masukkan angka valid'
                                    : null,
                                onChanged: (v) => setState(() {
                                  _tiers[i].price =
                                      double.tryParse(v) ?? _tiers[i].price;
                                }),
                              ),
                            ),
                            const SizedBox(width: 8),

                            // Hari (weekday / weekend)
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: _tiers[i].dayType,
                                decoration: const InputDecoration(
                                  labelText: 'Hari',
                                  border: OutlineInputBorder(),
                                ),
                                items: const [
                                  DropdownMenuItem(
                                    value: 'weekday',
                                    child: Text('Weekday'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'weekend',
                                    child: Text('Weekend'),
                                  ),
                                ],
                                onChanged: (v) => setState(() {
                                  _tiers[i].dayType = v!;
                                }),
                              ),
                            ),
                            const SizedBox(width: 8),

                            // Delete tier
                            IconButton(
                              icon: const Icon(Icons.delete),
                              color: Colors.red,
                              onPressed: () => setState(() {
                                _tiers.removeAt(i);
                              }),
                            ),
                          ],
                        ),
                      ),

                    // Add new tier button
                    TextButton.icon(
                      icon: const Icon(Icons.add, color: Colors.green),
                      label: const Text(
                        'Tambah Durasi',
                        style: TextStyle(color: Colors.green),
                      ),
                      onPressed: () => setState(() {
                        _tiers.add(TypePackagePrice(
                          tableType: widget.tableType,
                          packageId: _pkg!.id,
                          id: 0, // New tier will have no ID initially
                          duration: 1,
                          price: 0,
                          dayType: 'weekday',
                        ));
                      }),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              // — Save button —
              ElevatedButton.icon(
                onPressed: _save,
                icon: const Icon(Icons.save),
                label: const Text('Simpan'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[700],
                  minimumSize: const Size(double.infinity, 50),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
