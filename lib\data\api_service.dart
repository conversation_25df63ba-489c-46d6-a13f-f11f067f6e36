// import 'package:flutter/material.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../screens/settings_ip_page.dart';

// class LoginPage extends StatefulWidget {
//   const LoginPage({Key? key}) : super(key: key);
//   @override
//   _LoginPageState createState() => _LoginPageState();
// }

// class _LoginPageState extends State<LoginPage> {
//   // your existing controllers, e.g.:
//   final _usernameCtrl = TextEditingController();
//   final _passwordCtrl = TextEditingController();

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Login'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.settings),
//             tooltip: 'Server Settings',
//             onPressed: () {
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(builder: (_) =>  SettingsPage()),
//               );
//             },
//           ),
//         ],
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           children: [
//             TextField(
//               controller: _usernameCtrl,
//               decoration: const InputDecoration(labelText: 'Username'),
//             ),
//             const SizedBox(height: 12),
//             TextField(
//               controller: _passwordCtrl,
//               decoration: const InputDecoration(labelText: 'Password'),
//               obscureText: true,
//             ),
//             const SizedBox(height: 24),
//             ElevatedButton(
//               onPressed: _doLogin,
//               child: const Text('Log In'),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Future<void> _doLogin() async {
//     final prefs = await SharedPreferences.getInstance();
//     final serverIp = prefs.getString('server_ip') ?? 'localhost';
//     // pass `serverIp` into your ApiService or build URL manually
//     // … your existing login logic …
//   }
// }
