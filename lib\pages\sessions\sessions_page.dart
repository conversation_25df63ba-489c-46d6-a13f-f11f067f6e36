// // ignore_for_file: no_wildcard_variable_uses

// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';
// import 'package:provider/provider.dart';

// import '../../data/session_service.dart';
// import '../../models/session.dart';
// import 'session_form_page.dart';
// import 'countdown_timer.dart';
// import 'dart:async'; // ← timer
// import '../../data/relay_service.dart'; // ← your RelayService
// import '../../data/table_service.dart'; // ← your TableApiService

// class SessionsPage extends StatefulWidget {
//   const SessionsPage({super.key});
//   @override
//   State<SessionsPage> createState() => _SessionsPageState();
// }

// class _SessionsPageState extends State<SessionsPage> {
//   late final SessionService service;
//   late Future<List<Session>> _future;
//   String _search = '';
//   int _selectedStatus = -1; // -1 = Semua, 0 = Pending, 1 = Paid
//   DateTime? _filterDate;
//   late final RelayService _relayService;
//   late final TableApiService _tableService;

//   Timer? _relayTimer;
//   bool _initialized = false;

//   String _sortOption = 'default';
//   final List<String> _sortOptions = [
//     'default',
//     'cepat-ke-lama',
//     'lama-ke-cepat',
//   ];

//   // @override
//   // void initState() {
//   //   super.initState();
//   //   _relayService = context.read<RelayService>();
//   //   _tableService = context.read<TableApiService>();

//   //   // initial load + relay update
//   //   _load();

//   //   // re-sync every 10 seconds
//   //   _relayTimer = Timer.periodic(Duration(seconds: 10), (_) => _load());
//   // }

//   @override
//   void dispose() {
//     _relayTimer?.cancel();
//     super.dispose();
//   }

//   @override
//   void didChangeDependencies() {
//     super.didChangeDependencies();
//     service = context.read<SessionService>();
//     _relayService = context.read<RelayService>(); // ← ini
//     _tableService = context.read<TableApiService>(); // ← dan ini
//     if (!_initialized) {
//       _initialized = true;
//       _load();
//       _relayTimer = Timer.periodic(const Duration(seconds: 10), (_) => _load());
//     }
//   }

//   void _load() {
//     final now = DateTime.now();
//     final futureSessions = service.fetchSessions();
//     setState(() {
//       _future = futureSessions;
//       _filterDate = now;
//     });
//     futureSessions.then((sessions) => _syncRelays(sessions));
//   }

//   Future<void> _syncRelays(List<Session> sessions) async {
//     try {
//       // 1) Ambil daftar meja
//       final tables = await _tableService.fetchTables();
//       final now = DateTime.now();
//       final activeTableIds = <int>{};

//       // 2) Hitung ID meja yang sedang berjalan
//       for (final session in sessions) {
//         for (final d in session.billiardDetails) {
//           if (d.startTime == null || d.endTime == null) continue;
//           final start = _toDateTime(session, d.startTime!);
//           var end = _toDateTime(session, d.endTime!);
//           if (end.isBefore(start)) end = end.add(const Duration(days: 1));

//           if (now.isAfter(start) && now.isBefore(end)) {
//             activeTableIds.add(d.tableId);
//           }
//         }
//       }

//       // 3) Sinkronisasi relay & update DB
//       for (final t in tables) {
//         final isActive = activeTableIds.contains(t.id);

//         // Nyalakan relay jika perlu
//         if (isActive && t.status == 0) {
//           final ok = await _relayService.setRelay(t.relayId, true);
//           if (ok) {
//             await _tableService.updateRelayStatus(t.id, 1);
//           }
//         }
//         // Matikan relay jika perlu
//         else if (!isActive && t.status == 1) {
//           final ok = await _relayService.setRelay(t.relayId, false);
//           if (ok) {
//             await _tableService.updateRelayStatus(t.id, 0);
//           }
//         }
//       }
//     } catch (e) {
//       debugPrint('🔄 Sinkron relay gagal: $e');
//     }
//   }

//   void _openForm([Session? s]) async {
//     final res = await Navigator.push<bool>(
//       context,
//       MaterialPageRoute(builder: (_) => SessionFormPage(session: s)),
//     );
//     if (res == true) _load();
//   }

//   @override
//   Widget build(BuildContext ctx) {
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: Colors.green.shade700,
//         foregroundColor: Colors.white,
//         title: const Text(
//           'Sessions',
//           style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
//         ),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.refresh, color: Colors.white),
//             tooltip: 'Refresh Data',
//             onPressed: _load,
//           ),
//         ],
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(8),
//         child: Column(children: [
//           // Filter + Add button row
//           Row(
//             children: [
//               // 1) Guest search
//               Expanded(
//                 flex: 3,
//                 child: TextFormField(
//                   decoration: InputDecoration(
//                     hintText: 'Cari guest...',
//                     prefixIcon: const Icon(Icons.search),
//                     border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
//                   ),
//                   onChanged: (v) => setState(() => _search = v.toLowerCase()),
//                 ),
//               ),
//               const SizedBox(width: 8),

//               // 2) Date filter
//               Expanded(
//                 flex: 2,
//                 child: InkWell(
//                   onTap: () async {
//                     final d = await showDatePicker(
//                       context: context,
//                       initialDate: _filterDate ?? DateTime.now(),
//                       firstDate: DateTime(2020),
//                       lastDate: DateTime(2100),
//                     );
//                     if (d != null) setState(() => _filterDate = d);
//                   },
//                   child: InputDecorator(
//                     decoration: InputDecoration(
//                       labelText: 'Tanggal',
//                       border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
//                       suffixIcon: const Icon(Icons.calendar_today),
//                     ),
//                     child: Text(
//                       _filterDate != null ? DateFormat('yyyy-MM-dd').format(_filterDate!) : 'Semua tanggal',
//                     ),
//                   ),
//                 ),
//               ),
//               const SizedBox(width: 8),

//               // 3) Status dropdown
//               Expanded(
//                 flex: 2,
//                 child: DropdownButtonFormField<int>(
//                   value: _selectedStatus,
//                   items: const [
//                     DropdownMenuItem(value: -1, child: Text('Semua')),
//                     DropdownMenuItem(value: 0, child: Text('Pending')),
//                     DropdownMenuItem(value: 1, child: Text('Paid')),
//                   ],
//                   decoration: InputDecoration(
//                     labelText: 'Status',
//                     border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
//                   ),
//                   onChanged: (v) => setState(() => _selectedStatus = v!),
//                 ),
//               ),
//               const SizedBox(width: 8),

//               Expanded(
//                 flex: 2,
//                 child: DropdownButtonFormField<String>(
//                   value: _sortOption,
//                   items: const [
//                     DropdownMenuItem(value: 'default', child: Text('Default')),
//                     DropdownMenuItem(value: 'cepat-ke-lama', child: Text('Cepat → Lama')),
//                     DropdownMenuItem(value: 'lama-ke-cepat', child: Text('Lama → Cepat')),
//                   ],
//                   decoration: InputDecoration(
//                     labelText: 'Sortir Waktu',
//                     border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
//                   ),
//                   onChanged: (val) => setState(() => _sortOption = val!),
//                 ),
//               ),
//               const SizedBox(width: 8),

//               // 4) Add button
//               SizedBox(
//                 height: 48,
//                 child: ElevatedButton.icon(
//                   onPressed: () => _openForm(),
//                   icon: const Icon(Icons.add, size: 20),
//                   label: const Text('Tambah'),
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: Colors.green.shade700,
//                     foregroundColor: Colors.white,
//                     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
//                   ),
//                 ),
//               ),
//             ],
//           ),

//           const SizedBox(height: 12),

//           // Sessions grid
//           Expanded(
//             child: FutureBuilder<List<Session>>(
//               future: _future,
//               builder: (_, snap) {
//                 if (snap.connectionState == ConnectionState.waiting) {
//                   return const Center(child: CircularProgressIndicator());
//                 }
//                 if (snap.hasError) {
//                   return Center(child: Text('Error: ${snap.error}'));
//                 }
//                 // apply search + status filter
//                 // final filtered = snap.data!
//                 //     .where((s) =>
//                 //         s.guestName.toLowerCase().contains(_search) &&
//                 //         (_selectedStatus == -1 || s.status == _selectedStatus))
//                 //     .toList();
//                 final filtered = snap.data!.where((s) {
//                   final nameMatch = s.guestName.toLowerCase().contains(_search);
//                   final statusMatch = _selectedStatus == -1 ? true : (_selectedStatus == 0 && s.status == 'pending') || (_selectedStatus == 1 && s.status == 'paid');
//                   final dateMatch = _filterDate == null ? true : (s.date.year == _filterDate!.year && s.date.month == _filterDate!.month && s.date.day == _filterDate!.day);
//                   return nameMatch && statusMatch && dateMatch;
//                 }).toList();

//                 final now = DateTime.now();

//                 if (_sortOption == 'cepat-ke-lama') {
//                   filtered.sort((a, b) {
//                     final aTime = _earliestEndTime(a);
//                     final bTime = _earliestEndTime(b);

//                     final aExpired = aTime == null || aTime.isBefore(now);
//                     final bExpired = bTime == null || bTime.isBefore(now);

//                     if (aExpired && !bExpired) return 1;
//                     if (!aExpired && bExpired) return -1;
//                     if (aExpired && bExpired) return 0;

//                     return aTime!.compareTo(bTime!);
//                   });
//                 } else if (_sortOption == 'lama-ke-cepat') {
//                   filtered.sort((a, b) {
//                     final aTime = _earliestEndTime(a);
//                     final bTime = _earliestEndTime(b);

//                     final aExpired = aTime == null || aTime.isBefore(now);
//                     final bExpired = bTime == null || bTime.isBefore(now);

//                     if (aExpired && !bExpired) return 1;
//                     if (!aExpired && bExpired) return -1;
//                     if (aExpired && bExpired) return 0;

//                     return bTime!.compareTo(aTime!);
//                   });
//                 }

//                 if (filtered.isEmpty) return const Center(child: Text('No sessions'));

//                 return GridView.builder(
//                   physics: const BouncingScrollPhysics(),
//                   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: 4,
//                     crossAxisSpacing: 12,
//                     mainAxisSpacing: 12,
//                     childAspectRatio: 1.3,
//                   ),
//                   itemCount: filtered.length,
//                   itemBuilder: (_, i) => _buildCard(filtered[i]),
//                 );
//               },
//             ),
//           ),
//         ]),
//       ),
//     );
//   }

//   // DateTime? _earliestEndTime(Session s) {
//   //   final date = s.date;
//   //   final ends = s.billiardDetails.where((b) => b.endTime != null).map((b) => DateTime(date.year, date.month, date.day, b.endTime!.hour, b.endTime!.minute)).toList();
//   //   ends.sort();
//   //   return ends.isNotEmpty ? ends.first : null;
//   // }

//   DateTime getBusinessDate(DateTime dt) {
//     if (dt.hour < 3) {
//       final yesterday = dt.subtract(const Duration(days: 1));
//       return DateTime(yesterday.year, yesterday.month, yesterday.day);
//     }
//     return DateTime(dt.year, dt.month, dt.day);
//   }

//   // DateTime _toDateTime(Session s, TimeOfDay tod) {
//   //   final raw = DateTime(s.date.year, s.date.month, s.date.day, tod.hour, tod.minute);
//   //   final businessDate = getBusinessDate(raw);
//   //   return DateTime(businessDate.year, businessDate.month, businessDate.day, tod.hour, tod.minute);
//   // }

//   DateTime _toDateTime(Session s, TimeOfDay tod) {
//     return DateTime(s.date.year, s.date.month, s.date.day, tod.hour, tod.minute);
//   }

//   DateTime? _earliestEndTime(Session s) {
//     final ends = s.billiardDetails.where((b) => b.endTime != null).map((b) {
//       final start = _toDateTime(s, b.startTime!);
//       var end = _toDateTime(s, b.endTime!);
//       if (end.isBefore(start)) end = end.add(const Duration(days: 1));
//       return end;
//     }).toList();
//     ends.sort();
//     return ends.isNotEmpty ? ends.first : null;
//   }

//   Widget _buildCard(Session s) {
//     final fmtDate = DateFormat('dd-MMM-yyyy');
//     final fmtMoney = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ');
//     final now = DateTime.now();

//     final isPaid = s.status == 'paid';
//     final isPrinted = s.printedAt != null;

//     return Card(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//       elevation: 8,
//       child: Stack(
//         children: [
//           // ── Background + konten ────────────────────────
//           Container(
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [Color(0xFF29B6F6), Color(0xFF42A5F5)],
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//               ),
//               borderRadius: BorderRadius.all(Radius.circular(12)),
//             ),
//             padding: const EdgeInsets.all(12),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // Guest
//                 Text(
//                   s.guestName,
//                   style: const TextStyle(
//                     color: Colors.white,
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 4),

//                 // Date | Invoice
//                 Row(
//                   children: [
//                     Text(
//                       fmtDate.format(s.date),
//                       style: const TextStyle(color: Colors.white70, fontSize: 12),
//                     ),
//                     const Spacer(),
//                     Text(
//                       s.invoiceNumber,
//                       style: const TextStyle(color: Colors.white70, fontSize: 12),
//                     ),
//                   ],
//                 ),
//                 const SizedBox(height: 8),

//                 // Total
//                 Text(
//                   'Total: ${fmtMoney.format(s.totalAmount)}',
//                   style: const TextStyle(
//                     color: Colors.white,
//                     fontSize: 14,
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//                 const SizedBox(height: 8),

//                 // ── Billiard orders ─────────────────────────
//                 if (s.billiardDetails.isNotEmpty) ...[
//                   const Divider(color: Colors.white54),
//                   SizedBox(
//                     height: 40,
//                     child: ListView.builder(
//                       itemCount: s.billiardDetails.length,
//                       physics: const BouncingScrollPhysics(),
//                       itemBuilder: (_, i) {
//                         final b = s.billiardDetails[i];
//                         final sessionDay = DateTime(s.date.year, s.date.month, s.date.day);
//                         final startDt = DateTime(
//                           sessionDay.year,
//                           sessionDay.month,
//                           sessionDay.day,
//                           b.startTime!.hour,
//                           b.startTime!.minute,
//                         );
//                         var endDt = DateTime(
//                           sessionDay.year,
//                           sessionDay.month,
//                           sessionDay.day,
//                           b.endTime!.hour,
//                           b.endTime!.minute,
//                         );
//                         // jika end < start, tambahkan satu hari
//                         if (endDt.isBefore(startDt)) {
//                           endDt = endDt.add(const Duration(days: 1));
//                         }
//                         return Row(
//                           children: [
//                             Text(
//                               'Meja ${b.tableNumber}',
//                               style: const TextStyle(color: Colors.white70, fontSize: 12),
//                             ),
//                             const SizedBox(width: 8),
//                             Text(
//                               '${b.startTime!.format(context)}→${b.endTime!.format(context)}',
//                               style: const TextStyle(color: Colors.white70, fontSize: 12),
//                             ),
//                             const Spacer(),
//                             CountdownTimer(endTime: endDt),
//                           ],
//                         );
//                       },
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                 ],

//                 // ── Food orders ──────────────────────────────
//                 if (s.foodDetails.isNotEmpty) ...[
//                   const Divider(color: Colors.white54),
//                   SizedBox(
//                     height: 40,
//                     child: ListView.builder(
//                       itemCount: s.foodDetails.length,
//                       physics: const BouncingScrollPhysics(),
//                       itemBuilder: (_, i) {
//                         final f = s.foodDetails[i];
//                         return Text(
//                           '${f.foodName} x${f.quantity}',
//                           style: const TextStyle(color: Colors.white70, fontSize: 12),
//                         );
//                       },
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                 ],

//                 const Spacer(),

//                 // ── Actions ─────────────────────────────────
//                 Row(
//                   children: [
//                     // Tombol Edit
//                     Expanded(
//                       child: ElevatedButton.icon(
//                         onPressed: () => _openForm(s),
//                         icon: const Icon(Icons.edit, size: 16),
//                         label: const Text('Edit', style: TextStyle(fontSize: 12)),
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor: Colors.white24,
//                           foregroundColor: Colors.white,
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(6),
//                           ),
//                         ),
//                       ),
//                     ),
//                     const SizedBox(width: 6),

//                     // Tombol Delete
//                     // … di dalam Row(children: [ … ]),
//                     // Tombol Delete
//                     Expanded(
//                       child: // … di _buildCard(Session s) …
//                           ElevatedButton.icon(
//                         onPressed: () async {
//                           // 1) Cek boleh delete?
//                           if (s.printedAt != null || s.status == 'paid') {
//                             ScaffoldMessenger.of(context).showSnackBar(
//                               const SnackBar(
//                                 content: Text('Tidak boleh menghapus: session sudah diprint atau sudah lunas'),
//                               ),
//                             );
//                             return;
//                           }

//                           // 2) Konfirmasi awal
//                           final confirm = await showDialog<bool>(
//                             context: context,
//                             builder: (_) => AlertDialog(
//                               title: const Text('Konfirmasi Hapus'),
//                               content: const Text('Hapus session ini?'),
//                               actions: [
//                                 TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
//                                 ElevatedButton(
//                                   style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
//                                   onPressed: () => Navigator.pop(context, true),
//                                   child: const Text('Ya, Hapus', style: TextStyle(color: Colors.white)),
//                                 ),
//                               ],
//                             ),
//                           );
//                           if (confirm != true) return;

//                           // 3) Minta PIN
//                           final pinController = TextEditingController();
//                           final pinOk = await showDialog<bool>(
//                             context: context,
//                             barrierDismissible: false,
//                             builder: (_) => AlertDialog(
//                               title: const Text('Masukkan PIN'),
//                               content: TextField(
//                                 controller: pinController,
//                                 obscureText: true,
//                                 keyboardType: TextInputType.number,
//                                 decoration: const InputDecoration(
//                                   labelText: 'PIN',
//                                   hintText: '6 digit PIN Anda',
//                                 ),
//                               ),
//                               actions: [
//                                 TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
//                                 ElevatedButton(
//                                   onPressed: () {
//                                     // ← tambahkan ini
//                                     // validasi PIN (contoh hardcoded '1234'; ganti dengan logika nyata)
//                                     if (pinController.text == '416408' || pinController.text == 'qiuqiu') {
//                                       pinController.clear();
//                                       Navigator.pop(context, true);
//                                     } else {
//                                       ScaffoldMessenger.of(context).showSnackBar(
//                                         const SnackBar(content: Text('PIN salah')),
//                                       );
//                                       pinController.clear();
//                                     }
//                                   },
//                                   child: const Text('OK'),
//                                 ),
//                               ],
//                             ),
//                           );
//                           if (pinOk != true) return;

//                           // 4) Hapus sesungguhnya
//                           try {
//                             final ok = await service.deleteSession(s.id);
//                             if (!ok) throw Exception('Server error');
//                             _load(); // refresh
//                           } catch (e) {
//                             ScaffoldMessenger.of(context).showSnackBar(
//                               const SnackBar(content: Text('Gagal menghapus session')),
//                             );
//                           }
//                         },
//                         icon: const Icon(Icons.delete, size: 16),
//                         label: const Text('Delete', style: TextStyle(fontSize: 12)),
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor: Colors.red.shade600,
//                           foregroundColor: Colors.white,
//                           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),

//           // ── “Lunas” badge jika sudah paid ─────────────────
//           if (isPaid)
//             Positioned(
//               top: 8,
//               right: 8,
//               child: Image.asset(
//                 'assets/lunas.png',
//                 width: 200,
//                 height: 100,
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }

// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/session_service.dart';
import '../../models/session.dart';
import 'session_form_page.dart';
import 'countdown_timer.dart';
import 'dart:async'; // ← timer
import '../../data/relay_service.dart'; // ← your RelayService
import '../../data/table_service.dart'; // ← your TableApiService

class SessionsPage extends StatefulWidget {
  const SessionsPage({super.key});
  @override
  State<SessionsPage> createState() => _SessionsPageState();
}

class _SessionsPageState extends State<SessionsPage> {
  late final SessionService service;
  late final RelayService _relayService;
  late final TableApiService _tableService;

  final List<Session> _sessions = [];
  bool _loading = false;

  String _search = '';
  int _selectedStatus = -1; // -1 = Semua, 0 = Pending, 1 = Paid
  DateTime? _filterDate;

  String _sortOption = 'default';
  final List<String> _sortOptions = [
    'default',
    'cepat-ke-lama',
    'lama-ke-cepat',
  ];

  final ScrollController _scrollController = ScrollController();
  Timer? _relayTimer;
  bool _initialized = false;

  @override
  void dispose() {
    _relayTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      _initialized = true;
      service = context.read<SessionService>();
      _relayService = context.read<RelayService>();
      _tableService = context.read<TableApiService>();
      _filterDate = DateTime.now();
      _load();
      _relayTimer = Timer.periodic(const Duration(seconds: 10), (_) => _load());
    }
  }

  Future<void> _load() async {
    setState(() => _loading = true);
    try {
      final sessions = await service.fetchSessions();
      await _syncRelays(sessions);
      setState(() {
        _sessions
          ..clear()
          ..addAll(sessions);
        //_filterDate = DateTime.now();
      });
    } catch (e) {
      debugPrint('Error loading sessions: $e');
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _syncRelays(List<Session> sessions) async {
    try {
      final tables = await _tableService.fetchTables();
      final now = DateTime.now();
      final activeTableIds = <int>{};

      for (final session in sessions) {
        for (final d in session.billiardDetails) {
          if (d.startTime == null || d.endTime == null) continue;
          final start = _toDateTime(session, d.startTime!);
          var end = _toDateTime(session, d.endTime!);
          if (end.isBefore(start)) end = end.add(const Duration(days: 1));
          if (now.isAfter(start) && now.isBefore(end)) {
            activeTableIds.add(d.tableId);
          }
        }
      }

      for (final t in tables) {
        final isActive = activeTableIds.contains(t.id);
        if (isActive && t.status == 0) {
          final ok = await _relayService.setRelay(t.relayId, true);
          if (ok) await _tableService.updateRelayStatus(t.id, 1);
        } else if (!isActive && t.status == 1) {
          final ok = await _relayService.setRelay(t.relayId, false);
          if (ok) await _tableService.updateRelayStatus(t.id, 0);
        }
      }
    } catch (e) {
      debugPrint('🔄 Sinkron relay gagal: $e');
    }
  }

  void _openForm([Session? s]) async {
    final res = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => SessionFormPage(session: s)),
    );
    if (res == true) _load();
  }

  DateTime _toDateTime(Session s, TimeOfDay tod) {
    return DateTime(s.date.year, s.date.month, s.date.day, tod.hour, tod.minute);
  }

  DateTime? _earliestEndTime(Session s) {
    final ends = s.billiardDetails.where((b) => b.endTime != null).map((b) {
      final start = _toDateTime(s, b.startTime!);
      var end = _toDateTime(s, b.endTime!);
      if (end.isBefore(start)) end = end.add(const Duration(days: 1));
      return end;
    }).toList();
    ends.sort();
    return ends.isNotEmpty ? ends.first : null;
  }

  Widget _buildCard(Session s) {
    final fmtDate = DateFormat('dd-MMM-yyyy');
    final fmtMoney = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ');
    final now = DateTime.now();
    final isPaid = s.status == 'paid';
    // ── PPN & Grand Total ─────────────────────────────────
    final baseTotal = s.totalAmount;
    final ppnAmt    = (baseTotal * s.ppnRate / 100).round();
    final grandTotal = baseTotal + ppnAmt;

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 8,
      child: Stack(children: [
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF29B6F6), Color(0xFF42A5F5)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          padding: const EdgeInsets.all(12),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(s.guestName, style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Row(children: [
              Text(fmtDate.format(s.date), style: const TextStyle(color: Colors.white70, fontSize: 12)),
              const Spacer(),
              Text(s.invoiceNumber, style: const TextStyle(color: Colors.white70, fontSize: 12)),
            ]),
            // … earlier in your Column …
            const SizedBox(height: 8),

            // PPN line
            // Text(
            //   'PPN (${s.ppnRate.toStringAsFixed(0)}%): ${fmtMoney.format(ppnAmt)}',
            //   style: const TextStyle(
            //     color: Colors.white,
            //     fontSize: 14,
            //   ),
            // ),
            // const SizedBox(height: 4),

            // Total including PPN
            Text(
              'Total : ${fmtMoney.format(grandTotal)}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),

            if (s.billiardDetails.isNotEmpty) ...[
              const Divider(color: Colors.white54),
              SizedBox(
                height: 40,
                child: ListView.builder(
                  itemCount: s.billiardDetails.length,
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (_, i) {
                    final b = s.billiardDetails[i];
                    final sessionDay = DateTime(s.date.year, s.date.month, s.date.day);
                    final startDt = DateTime(sessionDay.year, sessionDay.month, sessionDay.day, b.startTime!.hour, b.startTime!.minute);
                    var endDt = DateTime(sessionDay.year, sessionDay.month, sessionDay.day, b.endTime!.hour, b.endTime!.minute);
                    if (endDt.isBefore(startDt)) endDt = endDt.add(const Duration(days: 1));
                    return Row(children: [
                      Text('Meja ${b.tableNumber}', style: const TextStyle(color: Colors.white70, fontSize: 12)),
                      const SizedBox(width: 8),
                      Text('${b.startTime!.format(context)}→${b.endTime!.format(context)}', style: const TextStyle(color: Colors.white70, fontSize: 12)),
                      const Spacer(),
                      CountdownTimer(endTime: endDt),
                    ]);
                  },
                ),
              ),
              const SizedBox(height: 8),
            ],
            if (s.foodDetails.isNotEmpty) ...[
              const Divider(color: Colors.white54),
              SizedBox(
                height: 40,
                child: ListView.builder(
                  itemCount: s.foodDetails.length,
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (_, i) {
                    final f = s.foodDetails[i];
                    return Text('${f.foodName} x${f.quantity}', style: const TextStyle(color: Colors.white70, fontSize: 12));
                  },
                ),
              ),
              const SizedBox(height: 8),
            ],
            const Spacer(),
            Row(children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _openForm(s),
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.white24, foregroundColor: Colors.white, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6))),
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    if (s.printedAt != null || s.status == 'paid') {
                      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Tidak boleh menghapus: session sudah diprint atau sudah lunas')));
                      return;
                    }
                    final confirm = await showDialog<bool>(
                      context: context,
                      builder: (_) => AlertDialog(
                        title: const Text('Konfirmasi Hapus'),
                        content: const Text('Hapus session ini?'),
                        actions: [
                          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                            onPressed: () => Navigator.pop(context, true),
                            child: const Text('Ya, Hapus', style: TextStyle(color: Colors.white)),
                          ),
                        ],
                      ),
                    );
                    if (confirm != true) return;
                    final pinController = TextEditingController();
                    final pinOk = await showDialog<bool>(
                      context: context,
                      barrierDismissible: false,
                      builder: (_) => AlertDialog(
                        title: const Text('Masukkan PIN'),
                        content: TextField(
                          controller: pinController,
                          obscureText: true,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(labelText: 'PIN', hintText: '6 digit PIN Anda'),
                        ),
                        actions: [
                          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
                          ElevatedButton(
                              onPressed: () {
                                if (pinController.text == '416408' || pinController.text == 'qiuqiu') {
                                  pinController.clear();
                                  Navigator.pop(context, true);
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('PIN salah')));
                                  pinController.clear();
                                }
                              },
                              child: const Text('OK')),
                        ],
                      ),
                    );
                    if (pinOk != true) return;
                    try {
                      final ok = await service.deleteSession(s.id);
                      if (!ok) throw Exception('Server error');
                      _load();
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Gagal menghapus session')));
                    }
                  },
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('Delete', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red.shade600, foregroundColor: Colors.white, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6))),
                ),
              ),
            ]),
          ]),
        ),
        if (isPaid)
          Positioned(
            top: 8,
            right: 8,
            child: Image.asset('assets/lunas.png', width: 200, height: 100),
          ),
      ]),
    );
  }

  @override
  Widget build(BuildContext ctx) {
    // apply search + status + date filter
    final now = DateTime.now();
    final filtered = _sessions.where((s) {
      final nameMatch = s.guestName.toLowerCase().contains(_search);
      final statusMatch = _selectedStatus == -1 ? true : (_selectedStatus == 0 && s.status == 'pending') || (_selectedStatus == 1 && s.status == 'paid');
      final dateMatch = _filterDate == null ? true : (s.date.year == _filterDate!.year && s.date.month == _filterDate!.month && s.date.day == _filterDate!.day);
      return nameMatch && statusMatch && dateMatch;
    }).toList();

    if (_sortOption == 'cepat-ke-lama') {
      filtered.sort((a, b) {
        final aTime = _earliestEndTime(a);
        final bTime = _earliestEndTime(b);
        final aExpired = aTime == null || aTime.isBefore(now);
        final bExpired = bTime == null || bTime.isBefore(now);
        if (aExpired && !bExpired) return 1;
        if (!aExpired && bExpired) return -1;
        if (aExpired && bExpired) return 0;
        return aTime!.compareTo(bTime!);
      });
    } else if (_sortOption == 'lama-ke-cepat') {
      filtered.sort((a, b) {
        final aTime = _earliestEndTime(a);
        final bTime = _earliestEndTime(b);
        final aExpired = aTime == null || aTime.isBefore(now);
        final bExpired = bTime == null || bTime.isBefore(now);
        if (aExpired && !bExpired) return 1;
        if (!aExpired && bExpired) return -1;
        if (aExpired && bExpired) return 0;
        return bTime!.compareTo(aTime!);
      });
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        title: const Text('Sessions', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18)),
        actions: [
          IconButton(icon: const Icon(Icons.refresh, color: Colors.white), tooltip: 'Refresh Data', onPressed: _load),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(children: [
          // Filter + Add button row
          Row(children: [
            Expanded(
              flex: 3,
              child: TextFormField(
                decoration: InputDecoration(
                  hintText: 'Cari guest...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
                onChanged: (v) => setState(() => _search = v.toLowerCase()),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 2,
              child: InkWell(
                onTap: () async {
                  final d = await showDatePicker(
                    context: context,
                    initialDate: _filterDate ?? DateTime.now(),
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2100),
                  );
                  if (d != null) setState(() => _filterDate = d);
                },
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Tanggal',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                    suffixIcon: const Icon(Icons.calendar_today),
                  ),
                  child: Text(_filterDate != null ? DateFormat('yyyy-MM-dd').format(_filterDate!) : 'Semua tanggal'),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 2,
              child: DropdownButtonFormField<int>(
                value: _selectedStatus,
                items: const [
                  DropdownMenuItem(value: -1, child: Text('Semua')),
                  DropdownMenuItem(value: 0, child: Text('Pending')),
                  DropdownMenuItem(value: 1, child: Text('Paid')),
                ],
                decoration: InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
                onChanged: (v) => setState(() => _selectedStatus = v!),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 2,
              child: DropdownButtonFormField<String>(
                value: _sortOption,
                items: const [
                  DropdownMenuItem(value: 'default', child: Text('Default')),
                  DropdownMenuItem(value: 'cepat-ke-lama', child: Text('Cepat → Lama')),
                  DropdownMenuItem(value: 'lama-ke-cepat', child: Text('Lama → Cepat')),
                ],
                decoration: InputDecoration(
                  labelText: 'Sortir Waktu',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                ),
                onChanged: (val) => setState(() => _sortOption = val!),
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              height: 48,
              child: ElevatedButton.icon(
                onPressed: () => _openForm(),
                icon: const Icon(Icons.add, size: 20),
                label: const Text('Tambah'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ),
          ]),
          const SizedBox(height: 12),
          // Sessions grid
          Expanded(
            child: Stack(children: [
              GridView.builder(
                controller: _scrollController,
                physics: const BouncingScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1.3,
                ),
                itemCount: filtered.length,
                itemBuilder: (_, i) => _buildCard(filtered[i]),
              ),
              if (_loading) const Center(child: CircularProgressIndicator()),
            ]),
          ),
        ]),
      ),
    );
  }
}
