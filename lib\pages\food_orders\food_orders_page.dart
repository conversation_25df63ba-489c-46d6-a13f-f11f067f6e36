// lib/features/pages/food_orders/food_orders_page.dart
// ignore_for_file: use_build_context_synchronously

//import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/food_order_service.dart';
import '../../models/food_order.dart';
//import '../../models/food_order_item.dart';
import 'food_order_form_page.dart';

class FoodOrdersPage extends StatefulWidget {
  const FoodOrdersPage({super.key});

  @override
  State<FoodOrdersPage> createState() => _FoodOrdersPageState();
}

class _FoodOrdersPageState extends State<FoodOrdersPage> {
  late final FoodOrderService apiService;
  List<FoodOrder> _orders = [];
  List<FoodOrder> _filtered = [];
  String _search = '';

  @override
  void initState() {
    super.initState();
    apiService = context.read<FoodOrderService>();
    _load();
  }

  Future<void> _load() async {
    try {
      // 1) Fetch orders
      final data = await apiService.fetchFoodOrders();
      // 2) For each order, fetch its items
      for (var o in data) {
        o.items = await apiService.fetchFoodOrderItems(o.id);
      }

      setState(() {
        _orders = data;
        _applyFilter();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal muat pesanan: $e')),
      );
    }
  }

  void _applyFilter() {
    setState(() {
      _filtered = _orders
          .where((o) => o.guestName.toLowerCase().contains(_search.toLowerCase()))
          .toList();
    });
  }

  Future<void> _gotoForm([FoodOrder? o]) async {
    final res = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => FoodOrderFormPage(order: o)),
    );
    if (res == true) _load();
  }

  Future<void> _confirmDelete(int id) async {
    final ok = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Konfirmasi Hapus'),
        content: const Text('Hapus pesanan ini?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Hapus', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
    if (ok == true) {
      await apiService.deleteFoodOrder(id);
      _load();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Food Orders'),
        backgroundColor: Colors.green.shade700,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _load),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            // Search + Tambah
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Cari...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    onChanged: (v) {
                      _search = v;
                      _applyFilter();
                    },
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: () => _gotoForm(),
                    icon: const Icon(Icons.add),
                    label: const Text('Tambah'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade700,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 10),

            // Grid kartu
            Expanded(
              child: _filtered.isEmpty
                  ? const Center(child: Text('Tidak ada pesanan'))
                  : GridView.builder(
                      physics: const BouncingScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                        childAspectRatio: 1.3,
                      ),
                      itemCount: _filtered.length,
                      itemBuilder: (ctx, i) => _buildCard(_filtered[i]),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCard(FoodOrder o) {
    final gradient = const LinearGradient(
      colors: [Color(0xFFEF6C00), Color(0xFFFFCA28)], // oranye → kuning
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
    final fmt = DateFormat('dd/MM HH:mm');
    final moneyFmt = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);
    // Daftar item (scrollable)
    final batches = o.items.map((e) => e.batch).toSet().toList()..sort();

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(gradient: gradient, borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // tombol di bawah
          children: [
            // Header
            Text(o.guestName,
                style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
            Text('Meja ${o.table['table_number']}',
                style: const TextStyle(color: Colors.white70, fontSize: 12)),
            Text(fmt.format(o.orderTime), style: const TextStyle(color: Colors.white70, fontSize: 12)),

            // Total
            Text(moneyFmt.format(o.totalAmount),
                style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600)),

            
            ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 80),
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  for (var batch in batches) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text(
                        'Batch #$batch',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    for (var item in o.items.where((i) => i.batch == batch))
                      Padding(
                        padding: const EdgeInsets.only(left: 8, bottom: 2),
                        child: Text(
                          '• ${item.menuItem['name']} ×${item.quantity}',
                          style: const TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                    const Divider(color: Colors.white30, height: 8),
                  ],
                ],
              ),
            ),

            // Tombol aksi
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _gotoForm(o),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white24,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // TODO: implement payment flow
                    },
                    icon: const Icon(Icons.payment, size: 16),
                    label: const Text('Bayar', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _confirmDelete(o.id),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('Hapus', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
