import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import '../data/session_service.dart';
import '../main.dart';

class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  String _serverIp = '';
  String _baseUrl = '';
  String _token = '';
  bool _hasToken = false;
  String _sessionTestResult = '';
  bool _testing = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final config = context.read<ServerConfig>();
    
    setState(() {
      _serverIp = prefs.getString('server_ip') ?? 'localhost';
      _baseUrl = config.baseUrl;
      _token = prefs.getString('token') ?? 'No token found';
      _hasToken = prefs.getString('token') != null;
    });
  }

  Future<void> _testSessionService() async {
    setState(() {
      _testing = true;
      _sessionTestResult = 'Testing...';
    });

    try {
      final sessionService = context.read<SessionService>();
      final sessions = await sessionService.fetchSessions();
      setState(() {
        _sessionTestResult = 'SUCCESS: Found ${sessions.length} sessions';
      });
    } catch (e) {
      setState(() {
        _sessionTestResult = 'ERROR: $e';
      });
    } finally {
      setState(() {
        _testing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Info'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Server Configuration', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text('Server IP: $_serverIp'),
                    Text('Base URL: $_baseUrl'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Authentication', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _hasToken ? Icons.check_circle : Icons.error,
                          color: _hasToken ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(_hasToken ? 'Token found' : 'No token found'),
                      ],
                    ),
                    if (_hasToken) ...[
                      const SizedBox(height: 8),
                      Text('Token: ${_token.length > 50 ? '${_token.substring(0, 50)}...' : _token}'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Session Service Test', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _testing ? null : _testSessionService,
                      child: _testing 
                        ? const SizedBox(
                            width: 16, 
                            height: 16, 
                            child: CircularProgressIndicator(strokeWidth: 2)
                          )
                        : const Text('Test Session Service'),
                    ),
                    const SizedBox(height: 8),
                    if (_sessionTestResult.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _sessionTestResult.startsWith('SUCCESS') 
                            ? Colors.green.withOpacity(0.1)
                            : Colors.red.withOpacity(0.1),
                          border: Border.all(
                            color: _sessionTestResult.startsWith('SUCCESS') 
                              ? Colors.green 
                              : Colors.red,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(_sessionTestResult),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Recommendations', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    if (!_hasToken)
                      const Text('• Please login first to get authentication token', style: TextStyle(color: Colors.red)),
                    if (_serverIp == 'localhost')
                      const Text('• Server IP is set to localhost - make sure server is running locally', style: TextStyle(color: Colors.orange)),
                    const Text('• Check if the server is running on port 4400'),
                    const Text('• Verify network connectivity to the server'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
