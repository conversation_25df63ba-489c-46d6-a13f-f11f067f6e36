// ignore_for_file: avoid_print

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/date_symbol_data_local.dart'; // ★

import 'data/auth_service.dart';
import 'data/food_order_service.dart';
import 'data/menu_item_service.dart';
import 'data/order_service.dart';
import 'data/relay_service.dart';
import 'data/report_service.dart';
import 'data/paket_service.dart';
import 'data/holiday_service.dart'; 
import 'data/session_service.dart';
import 'data/setting_service.dart';
import 'data/shift_service.dart';
import 'data/table_service.dart';
import 'data/user_service.dart';

// ← NEW: import the type‐package‐price service
import 'data/type_package_price_service.dart';

import 'screens/login_page.dart';

class ServerConfig {
  late final String baseUrl;
  Future<void> load() async {
    final prefs = await SharedPreferences.getInstance();
    final ip = prefs.getString('server_ip') ?? 'localhost';
    baseUrl = 'http://$ip:4400/api';
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inisialisasi locale untuk 'id_ID'
  await initializeDateFormatting('id_ID', null);  // ★

  final config = ServerConfig();
  await config.load();
  print('[APP] Using baseUrl: ${config.baseUrl}');

  runApp(
    MultiProvider(
      providers: [
        Provider<ServerConfig>.value(value: config),

        Provider<ApiService>(
          create: (_) => ApiService(config.baseUrl),
        ),
        Provider<TableApiService>(
          create: (_) => TableApiService(config.baseUrl),
        ),
        Provider<OrderService>(
          create: (_) => OrderService(config.baseUrl),
        ),
        Provider<RelayService>(
          create: (_) => RelayService(config.baseUrl),
        ),
        Provider<ReportService>(
          create: (_) => ReportService(config.baseUrl),
        ),

        Provider<RentalPackageService>(
          create: (_) => RentalPackageService(config.baseUrl),
        ),
        Provider<RentalShiftService>(
          create: (_) => RentalShiftService(config.baseUrl),
        ),
        Provider<FoodService>(
          create: (_) => FoodService(config.baseUrl),
        ),
        Provider<FoodOrderService>(
          create: (_) => FoodOrderService(config.baseUrl),
        ),
        Provider<SessionService>(
          create: (_) => SessionService(config.baseUrl),
        ),
        Provider<SettingService>(
          create: (_) => SettingService(config.baseUrl),
        ),
        Provider<HolidayService>(
          create: (_) => HolidayService(config.baseUrl),
        ),
        Provider<UserService>(
          create: (_) => UserService(config.baseUrl),
        ),
        // ← NEW: register the TypePackagePriceService
        Provider<TypePackagePriceService>(
          create: (_) => TypePackagePriceService(
            apiUrl: config.baseUrl,
          ),
        ),

      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    print('[APP] Aplikasi dimulai');

    return MaterialApp(
      title: 'Bilyard System',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Lato',
        textTheme: ThemeData.light().textTheme.apply(
              fontFamily: 'Lato',
            ),
      ),
      home: LoginPage(),
    );
  }
}
