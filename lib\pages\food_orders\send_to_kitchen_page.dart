// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

import '../../data/session_service.dart';
import '../../models/session.dart';

class SendToKitchenPage extends StatefulWidget {
  final int sessionId;
  final int batch;
  final String printerIp;
  final int printerPort;

  const SendToKitchenPage({
    super.key,
    required this.sessionId,
    required this.batch,
    this.printerIp = '*************',
    this.printerPort = 9100,
  });

  @override
  State<SendToKitchenPage> createState() => _SendToKitchenPageState();
}

class _SendToKitchenPageState extends State<SendToKitchenPage> {
  bool _isLoading = true;
  String _statusMessage = 'Mempersiapkan print...';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kirim ke Dapur'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isLoading)
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            const SizedBox(height: 16),
            Text(
              _statusMessage,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _isLoading ? null : () => Navigator.pop(context),
              child: const Text('Kembali'),
            ),
          ],
        ),
      ),
    );
  }

  String formatInfoLine(String label, String value) {
    const totalWidth = 47;
    final space = totalWidth - label.length - value.length;
    return label + ' ' * (space > 0 ? space : 1) + value;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sendKitchen();
    });
  }

  Future<void> _sendKitchen() async {
    final bytes = await _generateEscPosBytes(context.read<SessionService>());
    final uri = Uri.parse("http://************:4400/api/print-raw");
    final req = http.MultipartRequest('POST', uri)
      ..files.add(http.MultipartFile.fromBytes(
        'raw',
        bytes,
        filename: 'print.raw',
        contentType: MediaType('application', 'octet-stream'),
      ));

    setState(() => _statusMessage = 'Mengirim data ke server…');
    final streamed = await req.send();
    final resp = await http.Response.fromStream(streamed);

    if (resp.statusCode == 200) {
      setState(() {
        _isLoading = false;
        _statusMessage = '✅ Order berhasil dikirim ke dapur!';
      });
    } else {
      throw Exception('Server error: ${resp.statusCode} – ${resp.body}');
    }
  }

  String _format(DateTime dt) => DateFormat('dd/MM/yyyy HH:mm').format(dt.toLocal());

  Future<Uint8List> _generateEscPosBytes(SessionService sessionService) async {
    Session sess = await sessionService.fetchSessionById(widget.sessionId);
    final items = sess.foodDetails.where((d) => d.batch == widget.batch).toList();
    final dt = sess.createdAt ?? DateTime.now();
    final dateStr = _format(dt);
    final invoice = sess.invoiceNumber;
    final customer = sess.guestName;

    final bytes = BytesBuilder();

    // ESC/POS Commands
    Uint8List escAlign(int n) => Uint8List.fromList([27, 97, n]); // ESC a n
    Uint8List escStyle(int n) => Uint8List.fromList([27, 33, n]); // ESC ! n
    Uint8List lf([int count = 1]) => Uint8List.fromList(List.filled(count, 10)); // Line Feed
    Uint8List cut() => Uint8List.fromList([29, 86, 66, 0]); // GS V B (partial cut)
    Uint8List initialize() => Uint8List.fromList([27, 64]); // ESC @ (initialize)

    // Initialize printer
    bytes.add(initialize());

    // Header - Center aligned, bold & enlarged
    bytes.add(escAlign(1)); // Center
    bytes.add(escStyle(0x30)); // Bold + Double height + Double width
    bytes.add(utf8.encode('DEJAVU BILLIARD\n'));

    bytes.add(lf());

    // Reset to normal, left align
    bytes.add(escAlign(0)); // Left align
    bytes.add(escStyle(0x00)); // Normal

    // Order Info
    bytes.add(utf8.encode('${formatInfoLine('Tanggal', dateStr)}\n'));
    bytes.add(utf8.encode('${formatInfoLine('Invoice', invoice)}\n'));
    bytes.add(utf8.encode('${formatInfoLine('Customer', customer)}\n'));
    // ─── NEW: Table Number(s)
    final tableNumbers = sess.billiardDetails.map((d) => d.tableNumber.toString()).toSet().join(', ');
    bytes.add(utf8.encode('${formatInfoLine('Meja', tableNumbers)}\n'));
    bytes.add(utf8.encode('${formatInfoLine('Batch', widget.batch.toString())}\n'));

    bytes.add(lf());

    // Section header - center, bold
    bytes.add(escAlign(1)); // Center
    bytes.add(escStyle(0x08)); // Bold
    bytes.add(utf8.encode('ORDER F&B\n'));

    // Separator line
    bytes.add(escStyle(0x00)); // Normal
    bytes.add(utf8.encode('-----------------------------------------------\n'));

    // Items list - left align
    bytes.add(escAlign(0)); // Left align
    for (var item in items) {
      final name = item.foodName;
      final qty = item.quantity.toString();
      final space = 47 - name.length - qty.length - 1;
      final line = '$name${' ' * (space > 0 ? space : 1)}x$qty';
      bytes.add(utf8.encode('$line\n'));
    }

    // Footer spacing
    bytes.add(lf(3));

    // Print time
    bytes.add(escAlign(1)); // Center
    bytes.add(utf8.encode('Dicetak: ${_format(DateTime.now())}\n'));

    bytes.add(lf(3));
    bytes.add(cut()); // Cut paper

    return bytes.toBytes();
  }
}
