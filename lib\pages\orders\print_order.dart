// // lib/pages/print_invoice_page.dart

// import 'dart:typed_data';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:intl/intl.dart';
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
// import 'package:printing/printing.dart';
// import 'package:provider/provider.dart';

// import '../../models/session.dart';
// import '../../models/setting.dart';
// import '../../data/setting_service.dart';

// class PrintInvoicePage extends StatelessWidget {
//   final Session session;

//   const PrintInvoicePage({
//     super.key,
//     required this.session,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final receiptWidth = 200 * PdfPageFormat.mm;
//     final settingsSvc = context.read<SettingService>();

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text(
//           'Print Invoice',
//           style: TextStyle(color: Colors.white),
//         ),
//         backgroundColor: Colors.green.shade700,
//         iconTheme: const IconThemeData(color: Colors.white),
//       ),
//       body: PdfPreview(
//         maxPageWidth: receiptWidth,
//         build: (format) async {
//           // 1) load all settings once
//           final allSettings = await settingsSvc.fetchSettings();

//           // 2) extract footer_line_1…4
//           List<String> footers = List.generate(4, (i) {
//             final name = 'footer_line_${i + 1}';
//             final s = allSettings.firstWhere(
//               (st) => st.name == name,
//               orElse: () => Setting(id: 0, name: name, value: ''),
//             );
//             return s.value;
//           });

//           // 3) generate PDF with those footers
//           return _generatePdf(format, footers);
//         },
//       ),
//     );
//   }

//   Future<Uint8List> _generatePdf(
//     PdfPageFormat format,
//     List<String> footerLines,
//   ) async {
//     final pdf = pw.Document();

//     // load logo
//     final logoData = await rootBundle.load('assets/logo.jpg');
//     final logoImage = pw.MemoryImage(logoData.buffer.asUint8List());

//     final fmt = NumberFormat.currency(
//       locale: 'id_ID',
//       symbol: 'Rp ',
//       decimalDigits: 0,
//     );

//     final foodTotal = session.foodDetails.fold<double>(0, (sum, d) => sum + d.subtotal);
//     final billiardTotal = session.billiardDetails.fold<double>(0, (sum, b) => sum + b.price);
//     final subTotal = foodTotal + billiardTotal;
//     final discount     = session.discountAmount;
//     final taxRatePct   = session.taxRate;
//     final taxAmount    = session.taxAmount;
//     final afterService = subTotal - discount + taxAmount;
//     final ppnRatePct   = session.ppnRate;
//     final ppnAmount    = afterService * (ppnRatePct / 100);
//     final grandTotal   = afterService + ppnAmount;

//     pdf.addPage(
//       pw.Page(
//         pageFormat: PdfPageFormat(72 * PdfPageFormat.mm, double.infinity),
//         margin: const pw.EdgeInsets.all(10),
//         build: (context) => pw.Column(
//           crossAxisAlignment: pw.CrossAxisAlignment.start,
//           children: [
//             // — Header
//             pw.Center(child: pw.Image(logoImage, height: 70)),
//             pw.SizedBox(height: 4),
//             pw.Center(
//               child: pw.Text(
//                 'DEJAVU BILLIARD',
//                 style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
//               ),
//             ),
//             pw.Center(
//               child: pw.Text(
//                 'Jl. Danau Maninjau, Bukit Raya',
//                 style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
//               ),
//             ),
//             pw.Center(
//               child: pw.Text(
//                 'Kec. Sepaku, Kab. Penajam Paser Utara',
//                 style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
//               ),
//             ),
//             pw.Center(
//               child: pw.Text(
//                 'No WhatsApp: +62 852-4641-9331',
//                 style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
//               ),
//             ),
//             pw.Divider(),

//             // — Body
//             _row('Invoice', session.invoiceNumber),
//             _row(
//               'Tanggal',
//               DateFormat('dd/MM/yyyy HH:mm').format(session.createdAt!.toLocal()),
//             ),
//             _row('Cust.', session.guestName),
//             pw.SizedBox(height: 6),

//             pw.Center(
//               child: pw.Text('Billiard', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
//             ),
//             pw.Divider(),
//             ...session.billiardDetails.map((b) => _row(
//                   'Meja ${b.tableNumber}',
//                   '${b.duration} jam – ${fmt.format(b.price)}',
//                 )),

//             if (session.foodDetails.isNotEmpty) ...[
//               pw.SizedBox(height: 4),
//               pw.Center(child: pw.Text('F&B', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
//               pw.Divider(),
//               ...session.foodDetails.map((d) => _row(
//                     d.foodName,
//                     '${d.quantity} x Rp ${d.priceEach.toStringAsFixed(0)}',
//                   )),
//             ],

//             pw.SizedBox(height: 4),
//             pw.Divider(),
//             _row('Subtotal', fmt.format(subTotal)),
//             _row('Discount', fmt.format(discount)),
//             // include service rate
//             _row('Service (${taxRatePct.toStringAsFixed(0)}%)', fmt.format(taxAmount)),
//             // include PPN rate
//             _row('PPN (${ppnRatePct.toStringAsFixed(0)}%)', fmt.format(ppnAmount)),
//             pw.Divider(),
//             _row('Total', fmt.format(grandTotal), bold: true),

//             pw.SizedBox(height: 8),
//             pw.Center(child: pw.Text('TERIMA KASIH', style: pw.TextStyle(fontSize: 9))),

//             // — Custom footer lines from settings —
//             for (final line in footerLines)
//               if (line.trim().isNotEmpty)
//                 pw.Center(
//                   child: pw.Text(
//                     line,
//                     style: pw.TextStyle(fontSize: 7),
//                   ),
//                 ),
//           ],
//         ),
//       ),
//     );

//     return pdf.save();
//   }

//   pw.Widget _row(String label, String value, {bool bold = false}) {
//     return pw.Row(
//       mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//       children: [
//         pw.Text(
//           label,
//           style: pw.TextStyle(
//             fontSize: 9,
//             fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
//           ),
//         ),
//         pw.Text(
//           value,
//           style: pw.TextStyle(
//             fontSize: 9,
//             fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
//           ),
//         ),
//       ],
//     );
//   }
// }

// lib/pages/print_invoice_page.dart

import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

import '../../models/session.dart';
import '../../models/setting.dart';
import '../../data/setting_service.dart';

class PrintInvoicePage extends StatelessWidget {
  final Session session;

  const PrintInvoicePage({
    super.key,
    required this.session,
  });

  @override
  Widget build(BuildContext context) {
    final receiptWidth = 200 * PdfPageFormat.mm;
    final settingsSvc = context.read<SettingService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Print Invoice',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green.shade700,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: PdfPreview(
        maxPageWidth: receiptWidth,
        build: (format) async {
          // 1) load all settings once
          final allSettings = await settingsSvc.fetchSettings();

          // 2a) extract header_line_1…3
          final headerLines = List.generate(3, (i) {
            final name = 'header_line_${i + 1}';
            return allSettings
                .firstWhere(
                  (st) => st.name == name,
                  orElse: () => Setting(id: 0, name: name, value: ''),
                )
                .value;
          });

          // 2b) extract footer_line_1…4
          final footerLines = List.generate(4, (i) {
            final name = 'footer_line_${i + 1}';
            return allSettings
                .firstWhere(
                  (st) => st.name == name,
                  orElse: () => Setting(id: 0, name: name, value: ''),
                )
                .value;
          });

          // 3) generate PDF with headers + footers
          return _generatePdf(format, headerLines, footerLines);
        },
      ),
    );
  }

  Future<Uint8List> _generatePdf(
    PdfPageFormat format,
    List<String> headerLines,
    List<String> footerLines,
  ) async {
    final pdf = pw.Document();

    // load logo
    final logoData = await rootBundle.load('assets/logo.jpg');
    final logoImage = pw.MemoryImage(logoData.buffer.asUint8List());

    final fmt = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    final foodTotal = session.foodDetails.fold<double>(0, (sum, d) => sum + d.subtotal);
    final billiardTotal = session.billiardDetails.fold<double>(0, (sum, b) => sum + b.price);
    final subTotal = foodTotal + billiardTotal;
    final discount = session.discountAmount;
    final taxRatePct = session.taxRate;
    final taxAmount = session.taxAmount;
    final afterService = subTotal - discount + taxAmount;
    final ppnRatePct = session.ppnRate;
    final ppnAmount = afterService * (ppnRatePct / 100);
    final grandTotal = afterService + ppnAmount;

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat(72 * PdfPageFormat.mm, double.infinity),
        margin: const pw.EdgeInsets.all(10),
        build: (context) => pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // — Custom header lines from settings —
            // for (final line in headerLines)
            //   if (line.trim().isNotEmpty)
            //     pw.Center(
            //       child: pw.Text(
            //         line,
            //         style: pw.TextStyle(fontSize: 8, fontWeight: pw.FontWeight.bold),
            //       ),
            //     ),
            // pw.SizedBox(height: 4),

            // — Logo & Title —
            pw.Center(child: pw.Image(logoImage, height: 70)),
            pw.SizedBox(height: 4),

            // — Custom header lines from settings —
            for (final line in headerLines)
              if (line.trim().isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    line,
                    style: pw.TextStyle(fontSize: 8, fontWeight: pw.FontWeight.bold),
                  ),
                ),
            // pw.Center(
            //   child: pw.Text(
            //     'DEJAVU BILLIARD',
            //     style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            //   ),
            // ),
            // pw.Center(
            //   child: pw.Text(
            //     'Jl. Danau Maninjau, Bukit Raya',
            //     style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
            //   ),
            // ),
            // pw.Center(
            //   child: pw.Text(
            //     'Kec. Sepaku, Kab. Penajam Paser Utara',
            //     style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
            //   ),
            // ),
            // pw.Center(
            //   child: pw.Text(
            //     'No WhatsApp: +62 852-4641-9331',
            //     style: pw.TextStyle(fontSize: 7, fontWeight: pw.FontWeight.bold),
            //   ),
            // ),
            pw.Divider(),

            // — Body Order —
            _row('Invoice', session.invoiceNumber),
            _row(
              'Tanggal',
              DateFormat('dd/MM/yyyy HH:mm').format(session.createdAt!.toLocal()),
            ),
            _row('Cust.', session.guestName),
            pw.SizedBox(height: 6),

            pw.Center(child: pw.Text('Billiard', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
            pw.Divider(),
            ...session.billiardDetails.map((b) => _row(
                  'Meja ${b.tableNumber}',
                  '${b.duration} jam – ${fmt.format(b.price)}',
                )),

            if (session.foodDetails.isNotEmpty) ...[
              pw.SizedBox(height: 4),
              pw.Center(child: pw.Text('F&B', style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
              pw.Divider(),
              ...session.foodDetails.map((d) => _row(
                    d.foodName,
                    '${d.quantity} x Rp ${d.priceEach.toStringAsFixed(0)}',
                  )),
            ],

            pw.SizedBox(height: 4),
            pw.Divider(),
            _row('Subtotal', fmt.format(subTotal)),
            _row('Discount', fmt.format(discount)),
            _row('Service (${taxRatePct.toStringAsFixed(0)}%)', fmt.format(taxAmount)),
//             // include PPN rate
            _row('PPN (${ppnRatePct.toStringAsFixed(0)}%)', fmt.format(ppnAmount)),
            // _row('Service (\${taxRatePct.toStringAsFixed(0)}%)', fmt.format(taxAmount)),
            // _row('PPN (\${ppnRatePct.toStringAsFixed(0)}%)', fmt.format(ppnAmount)),
            pw.Divider(),
            _row('Total', fmt.format(grandTotal), bold: true),

            pw.SizedBox(height: 8),
            pw.Center(child: pw.Text('TERIMA KASIH', style: pw.TextStyle(fontSize: 9))),

            // — Custom footer lines from settings —
            for (final line in footerLines)
              if (line.trim().isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    line,
                    style: pw.TextStyle(fontSize: 7),
                  ),
                ),
          ],
        ),
      ),
    );

    return pdf.save();
  }

  pw.Widget _row(String label, String value, {bool bold = false}) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: 9,
            fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 9,
            fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
