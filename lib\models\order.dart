class Order {
  final int id;
  final int tableId;
  final int sessionId;
  final int packageId;
  final String tableNumber;
  final String packageName;
  final double duration;
  final double price;
  final double discount;
  final double foodTotal;
  final double billiardTotal;
  final int status;
  final DateTime startTime;
  final DateTime endTime;
  final DateTime? createdAt;

  Order({
    required this.id,
    required this.tableId,
    required this.sessionId,
    required this.packageId,
    required this.tableNumber,
    required this.packageName,
    required this.duration,
    required this.price,
    required this.discount,
    required this.foodTotal,
    required this.billiardTotal,
    required this.status,
    required this.startTime,
    required this.endTime,
    this.createdAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    double parseDouble(dynamic v) =>
        double.tryParse(v?.toString() ?? '') ?? 0.0;

    return Order(
      id: json['id'] as int,
      tableId: json['table_id'] as int,
      sessionId: json['session_id'] as int,
      packageId: json['package_id'] as int,
      tableNumber: json['table_number'] as String? ?? '',
      packageName: json['nama_paket'] as String? ?? '',
      duration: parseDouble(json['duration']),
      price: parseDouble(json['price']),
      discount: parseDouble(json['discount']),
      foodTotal: parseDouble(json['total_food']),
      billiardTotal: parseDouble(json['total_billiard']),
      status: json['status'] as int? ?? 0,
      startTime: DateTime.tryParse(json['start_time'] ?? '') ?? DateTime.now(),
      endTime: DateTime.tryParse(json['end_time'] ?? '') ?? DateTime.now(),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'table_id': tableId,
      'session_id': sessionId,
      'package_id': packageId,
      'table_number': tableNumber,
      'nama_paket': packageName,
      'duration': duration,
      'price': price,
      'discount': discount,
      'total_food': foodTotal,
      'total_billiard': billiardTotal,
      'status': status,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };
  }
}
