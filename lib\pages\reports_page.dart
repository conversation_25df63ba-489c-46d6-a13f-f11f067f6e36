import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../data/report_service.dart';
import '../../models/report.dart';

class ReportsPage extends StatefulWidget {
  const ReportsPage({super.key});

  @override
  State<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage> {
  late final ReportService reportService;
  ReportData? _reportData;
  bool isLoading = false;
  String? _error;

  // Month & Year selector
  late int _month;
  late int _year;
  late final List<int> _years;

  // Controllers for sticky header
  final ScrollController _hController = ScrollController();
  final ScrollController _vController = ScrollController();

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    _month = now.month;
    _year = now.year;
    _years = List.generate(5, (i) => now.year - i); // 5 tahun ke belakang
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    reportService = context.read<ReportService>();
  }

  @override
  void dispose() {
    _hController.dispose();
    _vController.dispose();
    super.dispose();
  }

  Future<void> _generate() async {
    setState(() {
      isLoading = true;
      _error = null;
    });
    final now = DateTime.now();
    final start = DateTime(_year, _month, 1);
    final end = (_year == now.year && _month == now.month) ? now : DateTime(_year, _month + 1, 0);

    try {
      final data = await reportService.fetchReport(start: start, end: end);
      setState(() => _reportData = data);
    } catch (e) {
      setState(() => _error = e.toString());
    } finally {
      setState(() => isLoading = false);
    }
  }

  String _formatDate(String iso) {
    final dt = DateTime.parse(iso);
    return DateFormat('dd-MMM').addPattern(' (E)').format(dt);
  }

  String _fmt(double v) => NumberFormat.currency(locale: 'id_ID', symbol: '', decimalDigits: 0).format(v);

  Widget _cell(String text, {bool bold = false, double width = 100}) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: bold ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final daily = _reportData?.daily ?? [];
    final summary = _reportData?.summary;
    final endLabel = summary == null
        ? ''
        : DateFormat('yyyy-MM-dd').format(
            (_year == DateTime.now().year && _month == DateTime.now().month) ? DateTime.now() : DateTime(_year, _month + 1, 0),
          );

    return Scaffold(
      appBar: AppBar(title: const Text('Laporan Harian')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // ── Picker ──────────────────────────────
            Row(children: [
              Expanded(
                child: DropdownButtonFormField<int>(
                  decoration: const InputDecoration(labelText: 'Bulan'),
                  value: _month,
                  items: List.generate(12, (i) {
                    final m = i + 1;
                    return DropdownMenuItem(
                      value: m,
                      child: Text(DateFormat.MMMM('id_ID').format(DateTime(0, m))),
                    );
                  }),
                  onChanged: (m) => setState(() => _month = m!),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<int>(
                  decoration: const InputDecoration(labelText: 'Tahun'),
                  value: _year,
                  items: _years.map((y) => DropdownMenuItem(value: y, child: Text('$y'))).toList(),
                  onChanged: (y) => setState(() => _year = y!),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: isLoading ? null : _generate,
                child: isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Generate'),
              ),
            ]),

            const SizedBox(height: 16),

            // ── Error ────────────────────────────────
            if (_error != null) Text(_error!, style: const TextStyle(color: Colors.red)),

            // ── Table dengan header sticky ───────────
            if (_reportData != null)
              Expanded(
                child: Scrollbar(
                  controller: _vController,
                  thumbVisibility: true,
                  child: Column(
                    children: [
                      // HEADER
                      SingleChildScrollView(
                        controller: _hController,
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            _cell('Tanggal', bold: true),
                            _cell('Dejavu Cash', bold: true),
                            _cell('Dejavu TF', bold: true),
                            _cell('Food Cash', bold: true),
                            _cell('Food TF', bold: true),
                            _cell('Service Cash', bold: true),
                            _cell('Service TF', bold: true),
                            _cell('Sarung Cash', bold: true),
                            _cell('Sarung TF', bold: true),
                            _cell('Total per Hari', bold: true),
                          ],
                        ),
                      ),

                      const Divider(height: 0),

                      // BODY
                      Expanded(
                        child: SingleChildScrollView(
                          controller: _hController,
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: 10 * 100.0,
                            child: ListView.builder(
                              controller: _vController,
                              itemCount: daily.length + 1,
                              itemBuilder: (context, idx) {
                                if (idx < daily.length) {
                                  final d = daily[idx];
                                  return Row(
                                    children: [
                                      _cell(_formatDate(d.date.toIso8601String())),
                                      _cell(_fmt(d.dejavuCash)),
                                      _cell(_fmt(d.dejavuTf)),
                                      _cell(_fmt(d.foodCash)),
                                      _cell(_fmt(d.foodTf)),
                                      _cell(_fmt(d.serviceCash)),
                                      _cell(_fmt(d.serviceTf)),
                                      _cell(_fmt(d.glovesCash)),
                                      _cell(_fmt(d.glovesTf)),
                                      _cell(_fmt(d.dailyTotal)),
                                    ],
                                  );
                                }
                                // summary row
                                return Container(
                                  color: Colors.grey.shade200,
                                  child: Row(
                                    children: [
                                      _cell('Total (s.d. $endLabel)', bold: true),
                                      _cell(_fmt(summary!.dejavuCash), bold: true),
                                      _cell(_fmt(summary.dejavuTf), bold: true),
                                      _cell(_fmt(summary.foodCash), bold: true),
                                      _cell(_fmt(summary.foodTf), bold: true),
                                      _cell(_fmt(summary.serviceCash), bold: true),
                                      _cell(_fmt(summary.serviceTf), bold: true),
                                      _cell(_fmt(summary.glovesCash), bold: true),
                                      _cell(_fmt(summary.glovesTf), bold: true),
                                      _cell(_fmt(summary.totalRevenue), bold: true),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
