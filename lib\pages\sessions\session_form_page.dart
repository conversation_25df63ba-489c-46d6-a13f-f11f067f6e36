// lib/features/pages/sessions/session_form_page.dart
// ignore_for_file: use_build_context_synchronously

import 'package:bilyard_flutter/data/holiday_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'dart:math';

import '../../data/session_service.dart';
import '../../data/setting_service.dart';
import '../../data/table_service.dart';
import '../../data/menu_item_service.dart';
import '../../data/relay_service.dart';
import '../../data/paket_service.dart';

import '../../models/session.dart';

import '../../models/table.dart';
import '../../models/food.dart';

import '../../models/paket.dart';
import '../../models/food_order_detail.dart';
import '../../models/billiard_order_detail.dart';
import '../food_orders/send_to_kitchen_page.dart';
import '../orders/print_order.dart';
import '../../data/type_package_price_service.dart';

class SessionFormPage extends StatefulWidget {
  final Session? session;
  const SessionFormPage({super.key, this.session});

  @override
  State<SessionFormPage> createState() => _SessionFormPageState();
}

class _SessionFormPageState extends State<SessionFormPage> {
  final _formKey = GlobalKey<FormState>();

  late final SessionService _sessionService;
  List<Session> _allSessions = [];
  late final TableApiService _tableService;
  late final FoodService _menuService;
  late final SettingService _settingService;
  late final RentalPackageService _packageService;

  Set<String> _holidayDates = {};

  // Map<int, List<Map<String, dynamic>>> packagePrices = {};
  Map<int, List<PriceTier>> packagePrices = {};
  late final TypePackagePriceService _typePriceSvc;
  // map override prices by “tableType_packageId”
  final Map<String, List<PriceTier>> _overridePrices = {};

  bool _loaded = false;

  List<BilliardTable> _tables = [];
  List<Food> _menuItems = [];

  List<RentalPackage> _packages = [];

  final List<_BilliardLine> _billiardLines = [];

  int _currentBatch = 1;
  final List<_FoodLine> _foodLines = [];

  final _guestNameCtrl = TextEditingController();
  final _guestPhoneCtrl = TextEditingController(text: '0');
  final _invoiceCtrl = TextEditingController();
  final _paymentCtrl = TextEditingController(text: '0');
  final _ppnCtrl = TextEditingController(text: '0');

  DateTime _date = DateTime.now();
  final int _status = 0; // 0=open,1=closed
  final double _payAmount = 0;

  // payment
  final _discountCtrl = TextEditingController(text: '0');
  final _taxRateCtrl = TextEditingController(); // default 10%
  String _paymentMethod = 'cash';
  final List<String> _methods = ['cash', 'debit', 'qris'];

  bool get _isEdit => widget.session != null;

  @override
  void initState() {
    super.initState();
    // <<< ADDED: Override _date sesuai “business date”
    // _date = _computeBusinessDate();
    _date = DateTime.now();
  }

  DateTime _computeBusinessDate() {
    final now = DateTime.now();
    if (now.hour < 3) {
      final yesterday = now.subtract(const Duration(days: 1));
      return DateTime(yesterday.year, yesterday.month, yesterday.day);
    } else {
      return DateTime(now.year, now.month, now.day);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_loaded) {
      _sessionService = context.read<SessionService>();
      _loadAllSessions();
      _tableService = context.read<TableApiService>();
      _menuService = context.read<FoodService>();
      _packageService = context.read<RentalPackageService>();
      _settingService = context.read<SettingService>();
      _typePriceSvc = context.read<TypePackagePriceService>();

      _loadLookups();
      _loaded = true;
    }
  }

  Future<void> _loadAllSessions() async {
    final sess = await _sessionService.fetchSessions();
    setState(() => _allSessions = sess);
  }

  DateTime _computeNextStart(int tableId) {
    final now = DateTime.now();
    // collect every end‐time + 10m buffer for that table
    final ends = <DateTime>[];
    for (var s in _allSessions) {
      for (var b in s.billiardDetails) {
        if (b.tableId == tableId && b.startTime != null) {
          final day = DateTime(s.date.year, s.date.month, s.date.day);
          final start = day.add(Duration(
            hours: b.startTime!.hour,
            minutes: b.startTime!.minute,
          ));
          // add buffer
          ends.add(start.add(Duration(hours: b.duration, minutes: 10)));
        }
      }
    }
    // if no bookings or all in past, start now
    if (ends.isEmpty) return now;
    // otherwise pick the latest
    final last = ends.reduce((a, b) => a.isAfter(b) ? a : b);
    return last.isAfter(now) ? last : now;
  }

  Future<void> _loadLookups() async {
    // Ambil daftar hari besar terlebih dahulu
    final holidayList = await context.read<HolidayService>().getAll();
    _holidayDates = holidayList.map((h) => DateFormat('yyyy-MM-dd').format(h.holidayDate)).toSet();

    // Kemudian ambil data lain
    final tablesFuture = _tableService.fetchTables();
    final menuFuture = _menuService.fetchFoods();
    final packsFuture = _packageService.getAllPackages();
    final taxFuture = _settingService.getSettingByName('tax_rate');
    final vatFuture = _settingService.getSettingByName('vat_rate');

    final tables = await tablesFuture;
    final menu = await menuFuture;
    final packs = await packsFuture;
    final tax = await taxFuture;
    final vat = await vatFuture;

    final pkgPrices = {for (var p in packs) p.id: p.harga};

    Session? sessionData;
    if (_isEdit) {
      sessionData = await _sessionService.fetchSessionById(widget.session!.id);
      final dayType = _computeDayType(sessionData.date); // Sekarang _holidayDates sudah terisi

      await Future.wait(sessionData.billiardDetails.map((b) {
        final tableType = tables.firstWhere((t) => t.id == b.tableId).type;
        final key = '${tableType}_${b.packageId}';
        return _typePriceSvc.fetch(tableType, b.packageId, dayType: dayType).then((raw) {
          _overridePrices[key] = raw.map((tp) => PriceTier(id: tp.id, duration: tp.duration, price: tp.price)).toList();
        });
      }));
    }

    // 4) Commit everything in one setState
    setState(() {
      _tables = tables;
      _menuItems = menu;
      _packages = packs;
      packagePrices = pkgPrices;
      _taxRateCtrl.text = tax?.value ?? '0';
      _ppnCtrl.text = vat?.value ?? '0';

      // clear existing lines
      _foodLines.clear();
      _billiardLines.clear();

      if (_isEdit && sessionData != null) {
        final s = sessionData;

        // — populate header fields —
        _guestNameCtrl.text = s.guestName;
        _guestPhoneCtrl.text = s.guestPhone ?? '';
        _invoiceCtrl.text = s.invoiceNumber;
        _date = s.date;
        _discountCtrl.text = s.discountPercent.toString();
        _taxRateCtrl.text = s.taxRate.toString();
        _ppnCtrl.text = s.ppnRate.toString();
        _paymentCtrl.text = s.payAmount.toString();
        _paymentMethod = s.paymentMethod!.toLowerCase();

        // — build food lines —
        int maxBatch = 0;
        for (var f in s.foodDetails) {
          final food = _menuItems.firstWhere(
            (m) => m.id == f.foodId,
            orElse: () => Food(
              id: f.foodId,
              name: f.foodName,
              category: '',
              description: '',
              price: f.priceEach,
              isAvailable: true,
            ),
          );
          final fl = _FoodLine(menuItem: food, batch: f.batch, quantity: f.quantity);
          fl.discountController.text = fl.discountPercent.toStringAsFixed(0);
          fl.taxController.text = fl.taxPercent.toStringAsFixed(0);
          fl.computeSubtotal();
          _foodLines.add(fl);
          maxBatch = max(maxBatch, f.batch);
        }
        _currentBatch = maxBatch + 1;

        // — build billiard lines dengan package-fallback —
        for (var b in s.billiardDetails) {
          final validPkgId = _packages.any((p) => p.id == b.packageId) ? b.packageId : _packages.first.id;

          final ln = _BilliardLine(
            tableId: b.tableId,
            packageId: validPkgId,
            duration: b.duration,
            selectedTierId: b.typePackagePriceId,
            status: b.status,
          );

          ln.startTime = b.startTime;
          ln.discountPercent = b.discount;
          ln.recompute(_tables, packagePrices, _overridePrices);
          _billiardLines.add(ln);
        }
      } else {
        _invoiceCtrl.text = 'INV${DateFormat('yyyyMMddHHmmss').format(DateTime.now())}';
      }
    });
  }

  /// helper widget for header values
  Widget _headerValue(String label, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
          const SizedBox(height: 4),
          Text(value, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildGradientHeader(BuildContext context) {
    final moneyFmt = NumberFormat.currency(
      locale: 'id_ID',
      symbol: 'Rp ',
      decimalDigits: 0,
    );

    // recompute totals inside header
    final totalBillard = _billiardLines.fold<double>(0, (sum, l) => sum + l.subtotal);
    final totalFood = _foodLines.fold<double>(0, (sum, l) => sum + l.subtotal);
    final subTotal = totalBillard + totalFood;

    final paymentAmt = double.tryParse(_paymentCtrl.text) ?? 0;
    final discount = double.tryParse(_discountCtrl.text) ?? 0;
    final taxRate = double.tryParse(_taxRateCtrl.text) ?? 0;

    final taxAmount = (subTotal - discount) * taxRate / 100;

    final discPct = double.tryParse(_discountCtrl.text) ?? 0;
    final discAmt = subTotal * discPct / 100;
    final taxPct = double.tryParse(_taxRateCtrl.text) ?? 0;
    final ppnPct = double.tryParse(_ppnCtrl.text) ?? 0; // ← read PPN
    final taxAmt = (subTotal - discAmt) * taxPct / 100;
    final afterTax = subTotal - discAmt + taxAmt;

    final ppnAmt = afterTax * ppnPct / 100; // ← compute PPN
    final totalAmt = afterTax + ppnAmt; // ← include PPN         // intermediate

    // sesudah menghitung totalAmt   (totalAmt = afterTax + ppnAmt)
    final double totalRounded = totalAmt.roundToDouble();
    final balance = paymentAmt - totalRounded;

    return Container(
      margin: const EdgeInsets.all(4),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.white, Color.fromARGB(255, 255, 208, 218)],
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // ─ Invoice / Date / Guest row ───────────────
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _invoiceCtrl,
                  readOnly: true,
                  decoration: _denseDecoration(label: 'Invoice #'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 1,
                child: TextFormField(
                  readOnly: true,
                  controller: TextEditingController(
                    text: DateFormat('yyyy-MM-dd').format(_date),
                  ),
                  decoration: _denseDecoration(label: 'Date').copyWith(suffixIcon: const Icon(Icons.calendar_today)),
                  onTap: _pickDate,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 3,
                child: TextFormField(
                  controller: _guestNameCtrl,
                  decoration: _denseDecoration(label: 'Guest Name'),
                  validator: (v) => v == null || v.isEmpty ? 'Required' : null,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _guestPhoneCtrl,
                  keyboardType: TextInputType.phone,
                  decoration: _denseDecoration(label: 'Phone'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 6),

          // ─ Sub-Totals row ────────────────────────────
          // ─ Sub-Totals row (all read-only fields) ────────────────────────────
          Row(
            children: [
              // Sub-Total
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(subTotal)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'Sub-Total'),
                ),
              ),
              const SizedBox(width: 8),

              // Food
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(totalFood)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'Food'),
                ),
              ),
              const SizedBox(width: 8),

              // Billiard
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(totalBillard)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'Billiard'),
                ),
              ),
              const SizedBox(width: 8),

              // Disc %
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _discountCtrl,
                  decoration: _denseDecoration(label: 'Disc %'),
                  keyboardType: TextInputType.number,
                  onChanged: (_) => setState(() {}),
                ),
              ),
              const SizedBox(width: 6),

              // Disc Amount
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(discAmt)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'Disc Amt'),
                ),
              ),
              const SizedBox(width: 6),

              // Service %
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _taxRateCtrl,
                  decoration: _denseDecoration(label: 'Service %'),
                  readOnly: true,
                  // keyboardType: TextInputType.number,
                  onChanged: (_) => setState(() {}),
                ),
              ),
              const SizedBox(width: 6),

              // Service Amount
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(taxAmt)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'S.C Amt'),
                ),
              ),
              const SizedBox(width: 6),

              // ← NEW PPN % FIELD
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _ppnCtrl,
                  decoration: _denseDecoration(label: 'PPN %'),
                  readOnly: true,
                  // keyboardType: TextInputType.number,
                  onChanged: (_) => setState(() {}),
                ),
              ),

              const SizedBox(width: 6),

              // ← NEW PPN Amount (read-only)
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(ppnAmt)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'PPN Amt'),
                ),
              ),

              const SizedBox(width: 6),

              // Total Amount
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(totalAmt)),
                  readOnly: true,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  decoration: _denseDecoration(label: 'Total'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 6),

          // ─ Payment / Balance row ─────────────────────
          Row(
            children: [
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<String>(
                  value: _paymentMethod,
                  items: _methods.map((m) => DropdownMenuItem(value: m.toLowerCase(), child: Text(m))).toList(),
                  onChanged: (v) => setState(() => _paymentMethod = v!),
                  decoration: _denseDecoration(label: 'Method'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _paymentCtrl,
                  decoration: _denseDecoration(label: 'Payment'),
                  keyboardType: TextInputType.number,
                  onChanged: (_) => setState(() {}),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: TextEditingController(text: moneyFmt.format(balance)),
                  readOnly: true,
                  decoration: _denseDecoration(label: 'Balance'),
                ),
              ),

              // now a dynamic text for balance
              //_headerValue('Balance', moneyFmt.format(balance)),
            ],
          ),
        ],
      ),
    );
  }

  /// small helper for dense InputDecoration
  InputDecoration _denseDecoration({String? label}) {
    return InputDecoration(
      labelText: label,
      border: OutlineInputBorder(),
      isDense: true,
      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
    );
  }

  /// Hitung apakah [date] masuk weekday/weekend/holiday
  String _computeDayType(DateTime date) {
    final key = DateFormat('yyyy-MM-dd').format(date);
    final isHoliday = _holidayDates.contains(key);
    final isWeekend = date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;
    return (isHoliday || isWeekend) ? 'weekend' : 'weekday';
  }

  /// PANEL UNTUK ADD / LIST BILLIARD LINES
  Widget _buildBilliardPanel() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color.fromARGB(255, 50, 50, 80),
            const Color.fromARGB(255, 30, 30, 60),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // scrollable list of existing lines
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 8),
                  ..._billiardLines.map(_buildBilliardRow),
                ],
              ),
            ),
          ),

          // tombol Add Table
          Align(
            alignment: Alignment.centerRight,
            child: ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              onPressed: () async {
                if (_tables.isEmpty || _packages.isEmpty) return;

                final defaultTableId = _tables.first.id;
                final defaultPackageId = _packages.first.id;
                final tableType = _tables.firstWhere((t) => t.id == defaultTableId).type;

                // tentukan dayType hari ini
                // final todayStr = DateFormat('yyyy-MM-dd').format(_date);
                // final isHoliday = _holidayDates.contains(todayStr);
                // final isWeekend = _date.weekday == DateTime.saturday ||
                //                   _date.weekday == DateTime.sunday;
                // final dayType = isHoliday || isWeekend ? 'weekend' : 'weekday';
                final dayType = _computeDayType(_date);

                try {
                  // fetch override dari backend
                  final raw = await _typePriceSvc.fetch(
                    tableType,
                    defaultPackageId,
                    dayType: dayType,
                  );

                  // convert ke PriceTier
                  final overrideTiers = raw
                      .map((tp) => PriceTier(
                            id: tp.id,
                            duration: tp.duration,
                            price: tp.price,
                          ))
                      .toList();

                  setState(() {
                    // simpan override ke map dengan key "<tableType>_<packageId>"
                    _overridePrices['${tableType}_$defaultPackageId'] = overrideTiers;

                    // buat line baru dan recompute
                    final line = _BilliardLine(
                      tableId: defaultTableId,
                      packageId: defaultPackageId,
                    );
                    line.recompute(_tables, packagePrices, _overridePrices);
                    _billiardLines.add(line);
                  });
                } catch (e) {
                  // kalau fetch gagal, tetap tambahkan line tanpa override
                  setState(() {
                    final line = _BilliardLine(
                      tableId: defaultTableId,
                      packageId: defaultPackageId,
                    );
                    line.recompute(_tables, packagePrices, _overridePrices);
                    _billiardLines.add(line);
                  });
                  print('❌ Gagal fetch override tiers: $e');
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Table'),
            ),
          ),
        ],
      ),
    );
  }

// helper to render a read-only value with an outline
  Widget _outlinedValue(String label, String value) {
    return Expanded(
      flex: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(color: Colors.white70, fontSize: 12)),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white70),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              value,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  // Calculate endTime from startTime + duration (in minutes)
  TimeOfDay? calculateEndTime(TimeOfDay? startTime, int durationInMinutes) {
    if (startTime == null) return null;
    final totalMinutes = startTime.hour * 60 + startTime.minute + durationInMinutes;
    final endHour = (totalMinutes ~/ 60) % 24;
    final endMinute = totalMinutes % 60;
    return TimeOfDay(hour: endHour, minute: endMinute);
  }

  /// SATU ROW BILLIARD ORDER
  Widget _buildBilliardRow(_BilliardLine ln) {
    final moneyFmt = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);

    // ambil tableType & key
    final tableType = _tables.firstWhere((t) => t.id == ln.tableId).type;
    final key = '${tableType}_${ln.packageId}';

    // pilih override jika ada, else default
    final tiers = _overridePrices[key] ?? packagePrices[ln.packageId] ?? <PriceTier>[];

    // pastikan harga & subtotal up-to-date
    ln.recompute(_tables, packagePrices, _overridePrices);

    // format start/end
    final startText = formatTimeOfDay(ln.startTime);
    TimeOfDay? maybeEnd;
    if (ln.startTime != null && ln.duration != null) {
      maybeEnd = calculateEndTime(ln.startTime, ln.duration! * 60);
    }
    final endText = formatTimeOfDay(maybeEnd);

    print('Tiers for key $key: ${tiers.map((t) => "id=${t.id}, dur=${t.duration}, price=${t.price}").toList()}');

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blueGrey.shade800,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // 1) Meja
            // Expanded(
            //   flex: 3,
            //   child: DropdownButtonFormField<int>(
            //     value: ln.tableId,
            //     dropdownColor: Colors.blueGrey.shade800,
            //     style: const TextStyle(color: Colors.white),
            //     decoration: InputDecoration(
            //       labelText: 'Meja',
            //       labelStyle: const TextStyle(color: Colors.white),
            //       border: const OutlineInputBorder(),
            //       isDense: true,
            //       enabledBorder: OutlineInputBorder(
            //         borderSide: BorderSide(color: Colors.white70),
            //       ),
            //     ),
            //     items: _tables
            //         .map((t) => DropdownMenuItem(
            //               value: t.id,
            //               child: Text('Meja ${t.tableNumber}', style: const TextStyle(color: Colors.white)),
            //             ))
            //         .toList(),
            //     onChanged: (v) async {
            //       // ganti meja, refetch override, recompute
            //       final newTableId = v!;
            //       final pkgId = ln.packageId!;
            //       setState(() => ln.tableId = newTableId);

            //       final tt = _tables.firstWhere((t) => t.id == newTableId).type;
            //       // final now = DateTime.now();
            //       // final dayType = (now.weekday == DateTime.saturday || now.weekday == DateTime.sunday)
            //       //     ? 'weekend'
            //       //     : 'weekday';

            //       final dayType = _computeDayType(_date);

            //       final raw = await _typePriceSvc.fetch(tt, pkgId, dayType: dayType);
            //       final overrideTiers = raw
            //           .map((tp) => PriceTier(
            //                 id: tp.id,
            //                 duration: tp.duration,
            //                 price: tp.price,
            //               ))
            //           .toList();

            //       setState(() {
            //         _overridePrices['${tt}_$pkgId'] = overrideTiers;
            //         ln.recompute(_tables, packagePrices, _overridePrices);
            //       });
            //     },
            //   ),
            // ),
            Expanded(
              flex: 3,
              child: _tables.isEmpty
                  ? const Center(child: CircularProgressIndicator())
                  : DropdownButtonFormField<int>(
                      value: _tables.any((t) => t.id == ln.tableId) ? ln.tableId : null,
                      dropdownColor: Colors.blueGrey.shade800,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        labelText: 'Meja',
                        labelStyle: const TextStyle(color: Colors.white),
                        border: const OutlineInputBorder(),
                        isDense: true,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.white70),
                        ),
                      ),
                      items: _tables
                          .map((t) => DropdownMenuItem(
                                value: t.id,
                                child: Text('Meja ${t.tableNumber}', style: const TextStyle(color: Colors.white)),
                              ))
                          .toList(),
                      onChanged: (v) async {
                        final newTableId = v!;
                        final pkgId = ln.packageId!;
                        setState(() => ln.tableId = newTableId);

                        final tt = _tables.firstWhere((t) => t.id == newTableId).type;
                        final dayType = _computeDayType(_date);

                        final raw = await _typePriceSvc.fetch(tt, pkgId, dayType: dayType);
                        final overrideTiers = raw
                            .map((tp) => PriceTier(
                                  id: tp.id,
                                  duration: tp.duration,
                                  price: tp.price,
                                ))
                            .toList();

                        setState(() {
                          _overridePrices['${tt}_$pkgId'] = overrideTiers;
                          ln.recompute(_tables, packagePrices, _overridePrices);
                        });
                      },
                    ),
            ),

            const SizedBox(width: 8),

            // 2) Paket
            Expanded(
              flex: 3,
              child: DropdownButtonFormField<int>(
                // value: ln.packageId,
                // jika paket lama sudah tidak ada di lookup, pakai `null` supaya dropdown reset
                value: _packages.any((p) => p.id == ln.packageId) ? ln.packageId : null,

                dropdownColor: Colors.blueGrey.shade800,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Paket',
                  labelStyle: const TextStyle(color: Colors.white),
                  border: const OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                items: _packages
                    .map((p) => DropdownMenuItem(
                          value: p.id,
                          child: Text(p.namaPaket, style: const TextStyle(color: Colors.white)),
                        ))
                    .toList(),
                onChanged: (newPkg) async {
                  if (newPkg == null || ln.tableId == null) return;

                  // hitung jenis hari: weekday/weekend/holiday
                  final tableType = _tables.firstWhere((t) => t.id == ln.tableId).type;
                  final dayType = _computeDayType(_date);

                  // ambil tiers terbaru dari backend
                  final raw = await _typePriceSvc.fetch(tableType, newPkg, dayType: dayType);
                  final overrideTiers = raw
                      .map((tp) => PriceTier(
                            id: tp.id,
                            duration: tp.duration,
                            price: tp.price,
                          ))
                      .toList();

                  setState(() {
                    // simpan paket baru
                    ln.packageId = newPkg;

                    // clear nilai durasi lama agar dropdown Durasi reset
                    ln.duration = null;
                    ln.selectedTierId = null;

                    // simpan override tiers di map
                    _overridePrices['${tableType}_$newPkg'] = overrideTiers;

                    // recompute supaya harga & subtotal benar
                    ln.recompute(_tables, packagePrices, _overridePrices);
                  });
                },
              ),
            ),

            const SizedBox(width: 8),

            Flexible(
              flex: 2,
              child: DropdownButtonFormField<int?>(
                isExpanded: true,
                value: ln.selectedTierId, // Use tier.id instead of duration
                hint: const Text('Pilih Durasi'),
                decoration: InputDecoration(
                  labelText: 'Durasi',
                  labelStyle: const TextStyle(color: Colors.white),
                  border: const OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                dropdownColor: Colors.blueGrey.shade800,
                style: const TextStyle(color: Colors.white),
                items: tiers
                    .map((tier) => DropdownMenuItem<int?>(
                          value: tier.id, // Unique ID for each tier
                          child: Text(
                            '${tier.duration} jam – Rp ${tier.price.toStringAsFixed(0)}',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ))
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    if (value != null) {
                      final sel = tiers.firstWhere((t) => t.id == value);
                      ln.duration = sel.duration; // Set duration based on selected tier
                      ln.selectedTierId = sel.id; // Store the selected tier ID
                    } else {
                      ln.duration = null;
                      ln.selectedTierId = null;
                    }
                    if (ln.startTime == null && ln.tableId != null) {
                      final nextDt = _computeNextStart(ln.tableId!);
                      ln.startTime = TimeOfDay(hour: nextDt.hour, minute: nextDt.minute);
                    }
                    ln.recompute(_tables, packagePrices, _overridePrices);
                  });
                },
              ),
            ),

            const SizedBox(width: 8),

            // 4) Start Time
            Flexible(
              flex: 2,
              child: TextFormField(
                readOnly: true,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Start',
                  labelStyle: TextStyle(color: Colors.white),
                  border: OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                controller: TextEditingController(text: startText),
                onTap: () async {
                  final t = await showTimePicker(
                    context: context,
                    initialTime: ln.startTime ?? TimeOfDay.now(),
                    builder: (ctx, child) => MediaQuery(
                      data: MediaQuery.of(ctx).copyWith(alwaysUse24HourFormat: true),
                      child: child!,
                    ),
                  );
                  if (t != null) setState(() => ln.startTime = t);
                },
              ),
            ),

            const SizedBox(width: 8),

            // 5) End Time
            Flexible(
              flex: 2,
              child: TextFormField(
                readOnly: true,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'End',
                  labelStyle: TextStyle(color: Colors.white),
                  border: OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                controller: TextEditingController(text: endText),
                textAlign: TextAlign.right,
              ),
            ),

            const SizedBox(width: 8),

            // 6) Disc %
            Flexible(
              flex: 1,
              child: TextFormField(
                initialValue: ln.discountPercent.toStringAsFixed(0),
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Disc %',
                  labelStyle: const TextStyle(color: Colors.white),
                  border: const OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                keyboardType: TextInputType.number,
                onChanged: (v) => setState(() {
                  ln.discountPercent = double.tryParse(v) ?? 0;
                  ln.recompute(_tables, packagePrices, _overridePrices);
                }),
              ),
            ),

            const SizedBox(width: 8),

            // 7) Harga
            Expanded(
              flex: 2,
              child: TextFormField(
                readOnly: true,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Harga',
                  labelStyle: TextStyle(color: Colors.white),
                  border: OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                controller: TextEditingController(text: moneyFmt.format(ln.price)),
                textAlign: TextAlign.right,
              ),
            ),

            const SizedBox(width: 8),

            // 8) Subtotal
            Expanded(
              flex: 2,
              child: TextFormField(
                readOnly: true,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Subtotal',
                  labelStyle: TextStyle(color: Colors.white),
                  border: OutlineInputBorder(),
                  isDense: true,
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white70),
                  ),
                ),
                controller: TextEditingController(text: moneyFmt.format(ln.subtotal)),
                textAlign: TextAlign.right,
              ),
            ),

            // 9) Hapus line
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () async {
                final confirm = await showDialog<bool>(
                  context: context,
                  builder: (ctx) => AlertDialog(
                    backgroundColor: Colors.blueGrey.shade800,
                    title: const Text('Confirm Delete', style: TextStyle(color: Colors.white)),
                    content: const Text('Remove this billiard line?', style: TextStyle(color: Colors.white)),
                    actions: [
                      TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel', style: TextStyle(color: Colors.white70))),
                      TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('Delete', style: TextStyle(color: Colors.redAccent))),
                    ],
                  ),
                );
                if (confirm == true) setState(() => _billiardLines.remove(ln));
              },
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to format TimeOfDay in 24-hour format
  String formatTimeOfDay(TimeOfDay? time) {
    if (time == null) return '';
    final hour = time.hour.toString().padLeft(2, '0'); // Ensures "08" instead of "8"
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  Widget _buildFoodPanel() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color.fromARGB(255, 6, 0, 42),
            const Color.fromARGB(255, 6, 0, 20),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // scrollable list of food rows
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 8),
                  ..._foodLines.map(_buildFoodRow),
                ],
              ),
            ),
          ),

          // fixed rectangle button at bottom-right
          Align(
            alignment: Alignment.centerRight,
            child: ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero, // rectangular
                ),
                backgroundColor: Colors.white,
                foregroundColor: Colors.blue.shade700,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onPressed: () => setState(() {
                // every new row uses the current batch
                _foodLines.add(_FoodLine(batch: _currentBatch));
              }),
              icon: const Icon(Icons.add),
              label: const Text('Add Food'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final panelHeight = MediaQuery.of(context).size.height * 0.25;

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEdit ? 'Edit Session' : 'New Session', style: const TextStyle(color: Colors.white)),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Header Section
            SizedBox(
              width: double.infinity,
              child: Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color.fromARGB(255, 255, 2, 6),
                      const Color.fromARGB(255, 255, 255, 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Text('Information', style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
              ),
            ),
            SizedBox(
              height: panelHeight,
              child: _buildGradientHeader(context),
            ),
            // Billiard Section
            SizedBox(
              width: double.infinity,
              child: Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color.fromARGB(255, 120, 0, 92),
                      const Color.fromARGB(255, 255, 255, 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Text('Billiard Order', style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
              ),
            ),
            SizedBox(
              height: panelHeight,
              child: _buildBilliardPanel(),
            ),
            // Food Section
            SizedBox(
              width: double.infinity,
              child: Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color.fromARGB(255, 46, 0, 118),
                      const Color.fromARGB(255, 255, 255, 255),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Text('Food Order', style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
              ),
            ),
            SizedBox(
              height: panelHeight,
              child: Row(
                children: [
                  Expanded(child: _buildFoodPanel()),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 160,
                    child: ElevatedButton.icon(
                      onPressed: _sendToKitchen,
                      icon: const Icon(Icons.send),
                      label: const Text('Send to Kitchen'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Action Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: _saveSession,
                    icon: const Icon(Icons.save),
                    label: Text(_isEdit ? 'Save Session' : 'Create Session'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () async {
                      // 1) Pastikan session sudah tersimpan
                      if (widget.session == null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Please save the session before printing.')),
                        );
                        return;
                      }

                      // 2) Tandai printed_at di backend
                      try {
                        await _sessionService.markPrinted(widget.session!.id);
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Gagal menandai print: $e')),
                        );
                        return;
                      }

                      // 3) Refresh parent dan navigasi ke halaman cetak
                      Navigator.pop(context, true); // kembali ke SessionsPage dengan hasil = true
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => PrintInvoicePage(session: widget.session!),
                        ),
                      );
                    },
                    icon: const Icon(Icons.print),
                    label: const Text('Print'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFoodRow(_FoodLine ln) {
    final moneyFmt = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // 1) Food selector
          Expanded(
            flex: 3,
            child: DropdownButtonFormField<Food>(
              style: const TextStyle(color: Colors.white),
              dropdownColor: Colors.blueGrey.shade800, // or whatever dark bg you’re using
              value: ln.menuItem,
              decoration: const InputDecoration(
                labelText: 'Food',
                labelStyle: TextStyle(color: Colors.white),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white),
                ),
                isDense: true,
              ),
              hint: const Text(
                'Select Food',
                style: TextStyle(color: Colors.white),
              ),
              items: _menuItems.map((m) {
                return DropdownMenuItem(
                  value: m,
                  child: Text(
                    '${m.name} — ${moneyFmt.format(m.price)} - ${m.tax.toStringAsFixed(0)}%',
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              }).toList(),
              onChanged: (m) => setState(() {
                ln.menuItem = m!;
                ln.foodId = m.id;
                ln.priceEach = m.price;
                ln.discountPercent = m.discountPercent;
                ln.taxPercent = m.tax;
                // push into controllers
                ln.discountController.text = ln.discountPercent.toStringAsFixed(0);
                ln.taxController.text = ln.taxPercent.toStringAsFixed(0);
                ln.computeSubtotal();
              }),
              // validator: (v) => v == null ? 'Req' : null,
            ),
          ),
          const SizedBox(width: 8),

          // 2) Batch (read-only)
          Expanded(
            flex: 1,
            child: TextFormField(
              style: const TextStyle(color: Colors.white),
              readOnly: true,
              controller: TextEditingController(text: ln.batch.toString()),
              decoration: const InputDecoration(
                labelText: 'Batch',
                labelStyle: TextStyle(color: Colors.white),
                border: OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                isDense: true,
              ),
            ),
          ),
          const SizedBox(width: 8),

          // 3) Quantity
          Expanded(
            flex: 2,
            child: TextFormField(
              style: const TextStyle(color: Colors.white),
              initialValue: ln.quantity.toString(),
              decoration: const InputDecoration(
                labelText: 'Qty',
                labelStyle: TextStyle(color: Colors.white),
                border: OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                isDense: true,
              ),
              keyboardType: TextInputType.number,
              onChanged: (v) => setState(() {
                ln.quantity = int.tryParse(v) ?? 1;
                ln.computeSubtotal();
              }),
              // validator: (v) => (int.tryParse(v ?? '') ?? 0) < 1 ? '>=1' : null,
            ),
          ),
          const SizedBox(width: 8),

          // 4) Discount %
          Expanded(
            flex: 1,
            child: TextFormField(
              style: const TextStyle(color: Colors.white),
              controller: ln.discountController,
              decoration: const InputDecoration(
                labelText: 'Disc %',
                labelStyle: TextStyle(color: Colors.white),
                border: OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                isDense: true,
              ),
              keyboardType: TextInputType.number,
              onChanged: (v) => setState(() {
                ln.discountPercent = double.tryParse(v) ?? 0;
                ln.computeSubtotal();
              }),
            ),
          ),
          const SizedBox(width: 8),

          // 5) Tax %
          Expanded(
            flex: 1,
            child: TextFormField(
              style: const TextStyle(color: Colors.white),
              controller: ln.taxController,
              readOnly: true,
              decoration: const InputDecoration(
                labelText: 'Tax %',
                labelStyle: TextStyle(color: Colors.white),
                border: OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                isDense: true,
              ),
              keyboardType: TextInputType.number,
              onChanged: (v) => setState(() {
                ln.taxPercent = double.tryParse(v) ?? 0;
                ln.computeSubtotal();
              }),
            ),
          ),
          const SizedBox(width: 8),

          // 6) Subtotal
          Expanded(
            flex: 2,
            child: TextFormField(
              style: const TextStyle(color: Colors.white),
              readOnly: true,
              decoration: const InputDecoration(
                labelText: 'Subtotal',
                labelStyle: TextStyle(color: Colors.white),
                border: OutlineInputBorder(borderSide: BorderSide(color: Colors.white)),
                isDense: true,
              ),
              controller: TextEditingController(
                text: moneyFmt.format(ln.subtotal),
              ),
              textAlign: TextAlign.right,
            ),
          ),

          // 7) Delete with confirmation
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () async {
              final confirm = await showDialog<bool>(
                context: context,
                builder: (ctx) => AlertDialog(
                  title: const Text('Confirm Delete'),
                  content: const Text('Remove this food line?'),
                  actions: [
                    TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel')),
                    TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('Delete')),
                  ],
                ),
              );
              if (confirm == true) setState(() => _foodLines.remove(ln));
            },
          ),
        ],
      ),
    );
  }

  // Future<void> _sendToKitchen() async {
  //   // 1) Gather all batch numbers from your _foodLines
  //   final batches = _foodLines.map((e) => e.batch).toSet().toList()..sort();
  //   if (batches.isEmpty) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(content: Text('Tidak ada item makanan yang ditambahkan.')),
  //     );
  //     return;
  //   }

  //   // 2) Ask the user which batch (if more than one)
  //   int selectedBatch;
  //   if (batches.length == 1) {
  //     selectedBatch = batches.first;
  //   } else {
  //     final batch = await showDialog<int>(
  //       context: context,
  //       barrierDismissible: true,
  //       builder: (ctx) {
  //         int temp = batches.first;
  //         return StatefulBuilder(
  //           builder: (ctx2, setStateDlg) => AlertDialog(
  //             shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
  //             title: Row(
  //               children: const [
  //                 Icon(Icons.info, color: Colors.blue, size: 28),
  //                 SizedBox(width: 8),
  //                 Text('Pilih Batch untuk Dikirim'),
  //               ],
  //             ),
  //             content: Column(
  //               mainAxisSize: MainAxisSize.min,
  //               children: batches.map((b) {
  //                 return RadioListTile<int>(
  //                   title: Text('Batch $b'),
  //                   value: b,
  //                   groupValue: temp,
  //                   onChanged: (v) => setStateDlg(() => temp = v!),
  //                 );
  //               }).toList(),
  //             ),
  //             actions: [
  //               TextButton(
  //                 onPressed: () => Navigator.pop(ctx2, null),
  //                 child: const Text('Batal'),
  //               ),
  //               ElevatedButton(
  //                 onPressed: () => Navigator.pop(ctx2, temp),
  //                 child: const Text('Lanjutkan'),
  //               ),
  //             ],
  //           ),
  //         );
  //       },
  //     );
  //     if (batch == null) return;
  //     selectedBatch = batch;
  //   }

  //   // 3) (Optional) mark “sent” on your backend
  //   // await _foodOrderService.sendToKitchen(widget.session!.id);

  //   // 4) Make sure this session has been saved (i.e. has an ID)
  //   final sessionId = widget.session?.id;
  //   if (sessionId == null) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(content: Text('Sesi belum disimpan. Simpan terlebih dahulu.')),
  //     );
  //     return;
  //   }

  //   // 5) Navigate into your preview/print page
  //   Navigator.push(
  //     context,
  //     MaterialPageRoute(
  //       builder: (_) => SendToKitchenPage(
  //         sessionId: sessionId,
  //         batch: selectedBatch,
  //         printerIp: '*************', // or load from SettingService
  //         printerPort: 9100,
  //       ),
  //     ),
  //   );
  // }

  Future<void> _sendToKitchen() async {
    // 1) Gather all batch numbers from your _foodLines
    final batches = _foodLines.map((e) => e.batch).toSet().toList()..sort();
    if (batches.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Tidak ada item makanan yang ditambahkan.')),
      );
      return;
    }

    // 2) Ask the user which batch (if more than one)
    int selectedBatch;
    if (batches.length == 1) {
      selectedBatch = batches.first;
    } else {
      final batch = await showDialog<int>(
        context: context,
        barrierDismissible: true,
        builder: (ctx) {
          int temp = batches.first;
          return StatefulBuilder(
            builder: (ctx2, setStateDlg) => AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              title: Row(
                children: const [
                  Icon(Icons.info, color: Colors.blue, size: 28),
                  SizedBox(width: 8),
                  Text('Pilih Batch untuk Dikirim'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: batches.map((b) {
                  return RadioListTile<int>(
                    title: Text('Batch $b'),
                    value: b,
                    groupValue: temp,
                    onChanged: (v) => setStateDlg(() => temp = v!),
                  );
                }).toList(),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(ctx2, null),
                  child: const Text('Batal'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(ctx2, temp),
                  child: const Text('Lanjutkan'),
                ),
              ],
            ),
          );
        },
      );
      if (batch == null) return;
      selectedBatch = batch;
    }

    // 3) Make sure this session has been saved (i.e. has an ID)
    final sessionId = widget.session?.id;
    if (sessionId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Sesi belum disimpan. Simpan terlebih dahulu.')),
      );
      return;
    }

    // 4) Get printer settings from SettingService or use default
    String printerIp = '*************'; // default
    int printerPort = 9100; // default

    try {
      // Optionally get from settings
      final printerIpSetting = await _settingService.getSettingByName('printer_ip');
      final printerPortSetting = await _settingService.getSettingByName('printer_port');

      if (printerIpSetting?.value.isNotEmpty == true) {
        printerIp = printerIpSetting!.value;
      }
      if (printerPortSetting?.value.isNotEmpty == true) {
        printerPort = int.tryParse(printerPortSetting!.value) ?? 9100;
      }
    } catch (e) {
      print('Warning: Could not get printer settings, using defaults: $e');
    }

    // 5) Navigate to the print page with correct IP/Port
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => SendToKitchenPage(
          sessionId: sessionId,
          batch: selectedBatch,
          printerIp: printerIp,
          printerPort: printerPort,
        ),
      ),
    );
  }

  Future<void> _pickDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _date,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      final businessDate = _computeBusinessDate();
      if (picked.isAfter(businessDate)) {
        // Jika user pilih tanggal > businessDate (jam < 3), tolak
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Karena sekarang jam < 03:00, tanggal maksimum adalah '
              '${DateFormat('yyyy-MM-dd').format(businessDate)}',
            ),
          ),
        );
        return;
      }
      setState(() => _date = picked);
    }
  }

  int _computeStatus(bool isPaid, _BilliardLine ln) {
    if (!isPaid) return ln.status;

    final now = TimeOfDay.now();
    final start = ln.startTime;
    if (start == null || ln.duration == null) return 2; // mark as done

    final startMins = start.hour * 60 + start.minute;
    final endMins = startMins + ln.duration! * 60;
    final nowMins = now.hour * 60 + now.minute;

    // Jika sekarang sebelum waktu selesai, tapi sudah bayar → selesaiin
    if (nowMins < endMins) return 2;

    return 2; // Sudah lewat pun tetap selesai
  }

  Future<void> _saveSession() async {
    final originalDetails = widget.session?.billiardDetails ?? [];

    debugPrint('⏳ Running form‐validation check…');
    debugPrint('  Guest name = "${_guestNameCtrl.text}"');
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      debugPrint('❌ Form.validate() returned false.');
      return;
    }

    for (var ln in _billiardLines) {
      if (ln.duration == null || ln.selectedTierId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a duration for every billiard line.')),
        );
        return;
      }
    }

    final billiardTotal = _billiardLines.fold<double>(0, (sum, l) => sum + l.subtotal);
    final foodTotal = _foodLines.fold<double>(0, (sum, l) => sum + l.subtotal);
    final subtotal = billiardTotal + foodTotal;

    final discPct = double.tryParse(_discountCtrl.text) ?? 0;
    final taxPct = double.tryParse(_taxRateCtrl.text) ?? 0;
    final ppnPct = double.tryParse(_ppnCtrl.text) ?? 0;

    final discAmt = subtotal * discPct / 100;
    final taxAmt = (subtotal - discAmt) * taxPct / 100;
    // final grandTotal = subtotal - discAmt + taxAmt;
    final ppnAmt = (subtotal - discAmt + taxAmt) * ppnPct / 100;

    // bulatkan ke rupiah terdekat
    final grandTotal = (subtotal - discAmt + taxAmt + ppnAmt).roundToDouble();

    // ───── menjadi ─────
    const double _TOLERANCE_RP = 3; // selisih receh yg ditoleransi

    final paymentAmt = double.tryParse(_paymentCtrl.text) ?? 0;

    // bulatkan total ke rupiah (tanpa sen) dulu
    final double grandTotalRounded = grandTotal.roundToDouble();

    // final balance = paymentAmt - grandTotalRounded;

    // // lunas bila selisih ±3 rupiah atau lebih besar
    // final isFullyPaid = (foodTotal + billiardTotal > 0) &&
    //                     balance.abs() <= _TOLERANCE_RP;

    // toleransi ≤ 3 rupiah
    final balance = (paymentAmt.round() - grandTotal).abs();
    final isFullyPaid = (foodTotal + billiardTotal > 0) && balance <= 3;

    final foodDetails = _foodLines
        .where((ln) => ln.menuItem != null)
        .map((ln) => FoodOrderDetail(
              id: 0,
              foodId: ln.menuItem!.id!,
              foodName: ln.menuItem!.name,
              batch: ln.batch,
              quantity: ln.quantity,
              priceEach: ln.priceEach,
              subtotal: ln.subtotal,
            ))
        .toList();

    final billiardDetails = _billiardLines.where((ln) => ln.tableId != null && ln.packageId != null).map((ln) {
      final existing = originalDetails.firstWhere(
        (d) => d.id == ln.selectedTierId,
        orElse: () => BilliardOrderDetail(
          id: 0,
          tableId: ln.tableId!,
          sessionId: widget.session?.id ?? 0,
          packageId: ln.packageId!,
          duration: ln.duration!,
          price: ln.price,
          discount: ln.discountPercent,
          typePackagePriceId: ln.selectedTierId!,
          status: 1,
          startTime: ln.startTime,
          endTime: ln.startTime == null
              ? null
              : TimeOfDay(
                  hour: (ln.startTime!.hour + ln.duration!) % 24,
                  minute: ln.startTime!.minute,
                ),
        ),
      );

      return BilliardOrderDetail(
        id: existing.id,
        tableId: ln.tableId!,
        sessionId: widget.session?.id ?? 0,
        packageId: ln.packageId!,
        duration: ln.duration!,
        price: ln.price,
        discount: ln.discountPercent,
        typePackagePriceId: ln.selectedTierId!,
        status: _computeStatus(isFullyPaid, ln),
        startTime: ln.startTime,
        endTime: ln.startTime == null
            ? null
            : TimeOfDay(
                hour: (ln.startTime!.hour + ln.duration!) % 24,
                minute: ln.startTime!.minute,
              ),
      );
    }).toList();

    final sess = Session(
      id: widget.session?.id ?? 0,
      guestName: _guestNameCtrl.text,
      guestPhone: _guestPhoneCtrl.text.isEmpty ? null : _guestPhoneCtrl.text,
      invoiceNumber: _invoiceCtrl.text,
      date: _date,
      status: isFullyPaid ? 'paid' : 'pending',
      totalFood: foodTotal,
      totalBilliard: billiardTotal,
      discountPercent: discPct,
      taxRate: taxPct,
      ppnRate: ppnPct, // ← new
      paymentMethod: _paymentMethod.toLowerCase(),
      paidAt: isFullyPaid ? DateTime.now() : null,
      payAmount: paymentAmt,
      foodDetails: foodDetails,
      billiardDetails: billiardDetails,
    );

    try {
      final ok = _isEdit ? await _sessionService.updateSession(sess) : await _sessionService.createSession(sess);

      if (ok) {
        // ✅ Jika fully paid, langsung matikan hardware relay & update status meja
        if (isFullyPaid) {
          final relayService = context.read<RelayService>();
          final relayDbService = context.read<TableApiService>();

          for (var ln in _billiardLines) {
            if (ln.tableId != null) {
              // 1) Cari relay_id dari daftar tabel
              final tbl = _tables.firstWhere((t) => t.id == ln.tableId);
              final relayId = tbl.relayId;

              try {
                // 2) Matikan hardware
                await relayService.setRelay(relayId, false);
                debugPrint('📴 Relay hardware OFF for relayId=$relayId');

                // 3) Update flag di DB
                await relayDbService.updateRelayStatus(ln.tableId!, 0);
                debugPrint('✅ Relay status DB updated to 0 for table ${ln.tableId}');
              } catch (e) {
                debugPrint('❌ Gagal matikan relay/meja ${ln.tableId}: $e');
              }
            }
          }
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("✅ Sesi berhasil disimpan. Meja disiapkan."),
            duration: Duration(seconds: 3),
          ),
        );

        await Future.delayed(const Duration(milliseconds: 500));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('❌ Gagal menyimpan sesi: Server error')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('❌ Gagal menyimpan sesi: $e')),
      );
    }
  }

  @override
  void dispose() {
    _guestNameCtrl.dispose();
    _guestPhoneCtrl.dispose();
    _invoiceCtrl.dispose();
    _discountCtrl.dispose();
    _taxRateCtrl.dispose();
    _ppnCtrl.dispose();
    _paymentCtrl.dispose();
    super.dispose();
  }
}

// ─── helper line-models ────────────────────────────
// ─── di atas class _BilliardLine ───
// ─── class _BilliardLine ────────────────────────────
class _BilliardLine {
  int? tableId;
  int? packageId;
  int? duration;
  int? selectedTierId;
  int status;
  double discountPercent = 0;
  double price = 0;
  double subtotal = 0;
  TimeOfDay? startTime;

  _BilliardLine({
    this.tableId,
    this.packageId,
    this.duration,
    this.selectedTierId,
    this.status = 1,
  });

  void recompute(
    List<BilliardTable> tables,
    Map<int, List<PriceTier>> defaults,
    Map<String, List<PriceTier>> overrides,
  ) {
    // if we don’t have table/package yet, zero out
    if (tableId == null || packageId == null) {
      price = subtotal = 0;
      selectedTierId = null;
      return;
    }

    // figure out tiers
    final tbl = tables.firstWhere((t) => t.id == tableId);
    final key = '${tbl.type}_$packageId';
    final tiers = overrides[key] ?? defaults[packageId] ?? [];

    if (tiers.isEmpty) {
      price = subtotal = 0;
      selectedTierId = null;
      return;
    }

    // find matching tier (duration is just for display, not for price computation)
    final tier = tiers.firstWhere((t) => t.duration == duration, orElse: () => tiers.first);

    price = tier.price; // this is already the total for the full duration
    subtotal = price * (1 - discountPercent / 100);
    selectedTierId = tier.id;
  }
}

class _FoodLine {
  int? foodId;
  Food? menuItem;
  int batch;
  int quantity;
  double priceEach;
  double discountPercent;
  double taxPercent;
  double subtotal;

  // new:
  final TextEditingController discountController;
  final TextEditingController taxController;

  _FoodLine({
    this.menuItem,
    this.batch = 1,
    this.quantity = 1,
  })  : foodId = menuItem?.id,
        priceEach = menuItem?.price ?? 0,
        discountPercent = menuItem?.discountPercent ?? 0,
        taxPercent = menuItem?.tax ?? 0,
        subtotal = 0, // initialize controllers with the starting values
        discountController = TextEditingController(
          text: (menuItem?.discountPercent ?? 0).toStringAsFixed(0),
        ),
        taxController = TextEditingController(
          text: (menuItem?.tax ?? 0).toStringAsFixed(0),
        ) {
    computeSubtotal();
  }

  /// Call this any time you change menuItem, quantity, discountPercent or taxPercent.
  void computeSubtotal() {
    // apply discount then tax per item, then multiply by quantity
    final afterDiscount = priceEach * (1 - discountPercent / 100);
    final afterTax = afterDiscount * (1 + taxPercent / 100);
    subtotal = afterTax * quantity;
  }
}
