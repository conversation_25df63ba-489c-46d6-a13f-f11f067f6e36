import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../data/session_service.dart';
import '../../models/session.dart';
import '../../data/table_service.dart';
import '../data/relay_service.dart';
import '../../models/billiard_order_detail.dart';
// sesuaikan path relatifnya dengan lokasi filemu
import '../../models/relay.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/widgets.dart'; // sudah include kalau pakai Material

/// 2) Model ringkas untuk satu entry waiting list
class _WaitingEntry {
  final String guestName;
  final String tableNumber;
  final String type;
  final DateTime arrivalTime;
  final DateTime endTime;    // ← tambah endTime

  _WaitingEntry({
    required this.guestName,
    required this.tableNumber,
    required this.type,
    required this.arrivalTime,
    required this.endTime,    // ←
  });
}

class BilliardDashboard extends StatefulWidget {
  const BilliardDashboard({super.key});

  @override
  State<BilliardDashboard> createState() => _BilliardDashboardState();
}

class _BilliardDashboardState extends State<BilliardDashboard> {
  late final TableApiService _tableService;
  late final SessionService _sessionService;
  late final RelayService _relayService; // ← deklarasi RelayService
  // List<dynamic> _tables = [];
  List<dynamic> _tables = [];
  List<Session> _sessions = [];

  bool _initialized = false;
  bool _isLoading = true;
  String? _error;
  DateTime? _lastFetch;

  static double _TOLERANCE_RUPIAH = 3.0;

  Timer? _refreshTimer;
  Timer? _countdownTimer;

  Color typeColor(String type) {
    switch (type.toUpperCase()) {
      case 'VVIP':
        return Colors.purple.shade200; // lavender pastel
      case 'PREMIUM':
        return Colors.lightBlue.shade200; // light sky-blue
      case 'PRESIDENT':
        return Colors.orange.shade200; // soft peach
      case 'EXCLUSIVE':
        return Colors.teal.shade200; // mint pastel
      default:
        return Colors.grey.shade300; // netral ringan
    }
  }

  // ─────────────────────────────────────────────────────────────
  // Anggap lunas bila selisih ≤ 3 rupiah
  // ─────────────────────────────────────────────────────────────
  

  bool _isSessionSettled(Session s) {
    // Total tagihan lengkap (sudah termasuk diskon, PPN, pajak) 
    // backend-nya sudah menghitung ke kolom total_amount
    final totalDue = s.totalAmount;

    // Jika ingin pakai per-item bisa tetap pakai fold seperti kemarin,
    // tapi paling praktis membaca totalAmount saja.
    return s.payAmount + _TOLERANCE_RUPIAH >= totalDue;
  }



  /// 1) Method untuk show waiting list di modal bottom sheet
  final DateFormat timeFmt = DateFormat('HH:mm');

  void _showWaitingList() {
    final now = DateTime.now();
    final List<_WaitingEntry> waiting = [];

    // kumpulkan semua antrean (startTime di masa depan)
    for (final s in _sessions) {
      for (final d in s.billiardDetails) {
        if (d.startTime == null) continue;
        final arrival = _toDateTime(s, d.startTime!);
        if (arrival.isAfter(now)) {
          // hitung endTime
          var end = arrival.add(Duration(hours: d.duration));
          // jika melewati tengah malam:
          if (end.isBefore(arrival)) end = end.add(const Duration(days: 1));
          final tbl = _tables.firstWhere(
            (t) => t['table_number'] == d.tableNumber,
            orElse: () => {'type': 'STANDARD'},
          );
          waiting.add(_WaitingEntry(
            guestName: s.guestName,
            tableNumber: d.tableNumber,
            type: tbl['type']?.toString().toUpperCase() ?? 'STANDARD',
            arrivalTime: arrival,
            endTime:     end,      // ←
          ));
        }
      }
    }

    // group by type & sort per grup
    final byType = <String, List<_WaitingEntry>>{};
    for (var w in waiting) {
      byType.putIfAbsent(w.type, () => []).add(w);
    }
    byType.forEach((_, list) => list.sort((a, b) => a.arrivalTime.compareTo(b.arrivalTime)));

    // tampilkan bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => DraggableScrollableSheet(
        expand: false,
        builder: (_, ctrl) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text('Waiting List', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Expanded(
                child: ListView(
                  controller: ctrl,
                  children: byType.entries.map((e) {
                    return ExpansionTile(
                      title: Text('${e.key} (${e.value.length})'),
                      children: e.value.map((we) {
                        return ListTile(
                          leading: const Icon(Icons.person),
                          title: Text(we.guestName),
                          subtitle: Text(
                            'Meja ${we.tableNumber} • '
                            '${timeFmt.format(we.arrivalTime)} – ${timeFmt.format(we.endTime)}',
                          ),
                        );
                      }).toList(),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget typeStatsCards(Map<String, Map<String, int>> typeStats) {
    return GridView.count(
      crossAxisCount: 5,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 2.8,
      children: typeStats.entries.map((entry) {
        final type = entry.key;
        final s = entry.value;
        return Card(
          elevation: 8,
          color: typeColor(type).withOpacity(0.6),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(type, textAlign: TextAlign.center, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black)),
                const SizedBox(height: 4),
                Text(
                  'Total: ${s['total']} | Tersedia: ${s['tersedia']} | Dipakai: ${s['dipakai']}',
                  style: const TextStyle(color: Colors.black, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Map<String, Map<String, int>> _buildTypeStats() {
    const knownTypes = ['STANDARD', 'VVIP', 'PREMIUM', 'PRESIDENT', 'EXCLUSIVE'];
    final Map<String, Map<String, int>> stats = {
      for (final t in knownTypes) t: {'total': 0, 'dipakai': 0, 'tersedia': 0}
    };

    for (final t in _tables) {
      final type = (t['type'] ?? 'STANDARD').toString().toUpperCase();
      final status = t['status'] ?? 0;

      if (!stats.containsKey(type)) {
        stats[type] = {'total': 0, 'dipakai': 0, 'tersedia': 0};
      }

      stats[type]!['total'] = stats[type]!['total']! + 1;

      if (status == 1) {
        stats[type]!['dipakai'] = stats[type]!['dipakai']! + 1;
      } else {
        stats[type]!['tersedia'] = stats[type]!['tersedia']! + 1;
      }
    }

    return stats;
  }

  @override
Widget build(BuildContext context) {
  return Stack(
    children: [
      // 1) watermark sebagai layer paling bawah
      Positioned.fill(
        child: Image.asset(
          'assets/logo.jpg',
          fit: BoxFit.contain,    // penuh di belakang
          opacity: const AlwaysStoppedAnimation(0.1), // ← gunakan ini         // agar samar
        ),
      ),

      // 2) Scaffold transparan di atas watermark
      Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: false,   // AppBar ikut transparan
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.green.shade800.withOpacity(0.8),
          elevation: 0,
          title: const Text('DEJAVU BILLIARD', style: TextStyle(color: Colors.white)),
          actions: [
            IconButton(
              icon: const Icon(Icons.queue, color: Colors.white),
              tooltip: 'Waiting List',
              onPressed: _showWaitingList,
            ),
            IconButton(
              icon: const Icon(Icons.flashlight_on, color: Colors.white),
              tooltip: 'Resend Relay Commands',
              onPressed: _resendRelayCommands,
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadData,
            ),
            IconButton(
              icon: const Icon(Icons.email, color: Colors.white),
              tooltip: 'Kirim Report Manual',
              onPressed: _sendReport,
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _error != null
                ? Center(child: Text(_error!, style: const TextStyle(color: Colors.red)))
                : Column(
                    children: [
                      _buildSummary(),
                      Expanded(child: _buildGrid()),
                    ],
                  ),
      ),
    ],
  );
}


  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (!_initialized) {
      _tableService = context.read<TableApiService>();
      _sessionService = context.read<SessionService>();
      _relayService = context.read<RelayService>(); // ← inisialisasi

      // initial load with spinner & error handling
      _loadData();

      // every-10s silent refresh (no spinner)
      _refreshTimer = Timer.periodic(
        const Duration(seconds: 1),
        (_) => _refreshData(),
      );

      _initialized = true;
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _countdownTimer?.cancel();
    super.dispose();
  }

  Widget _buildGrid() {
    final isWide = MediaQuery.of(context).size.width > 600;
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6, // atau 7
        childAspectRatio: 1.4,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
      ),
      itemCount: _tables.length,
      itemBuilder: (_, i) => _buildTableCard(_tables[i]),
    );
  }

  // add this helper to your BilliardDashboard class:

  // DateTime _toDateTime(Session s, TimeOfDay tod) {
  //   final raw = DateTime(s.date.year, s.date.month, s.date.day, tod.hour, tod.minute);
  //   final businessDate = getBusinessDate(raw);
  //   return DateTime(businessDate.year, businessDate.month, businessDate.day, tod.hour, tod.minute);
  // }

  DateTime _toDateTime(Session s, TimeOfDay tod) {
    return DateTime(s.date.year, s.date.month, s.date.day, tod.hour, tod.minute);
  }

  // Widget _buildSummary() {
  //   final total = _tables.length;
  //   final now = DateTime.now();
  //   final occupiedTableNos = <String>{};

  //   for (final s in _sessions) {
  //     for (final d in s.billiardDetails) {
  //       if (d.startTime == null || d.endTime == null) continue;

  //       // Hitung waktu berdasarkan business date
  //       // final rawStart = DateTime(s.date.year, s.date.month, s.date.day, d.startTime!.hour, d.startTime!.minute);
  //       // final rawEnd = DateTime(s.date.year, s.date.month, s.date.day, d.endTime!.hour, d.endTime!.minute);

  //       // final businessStart = getBusinessDate(rawStart).add(Duration(
  //       //   hours: d.startTime!.hour,
  //       //   minutes: d.startTime!.minute,
  //       // ));
  //       // var businessEnd = getBusinessDate(rawEnd).add(Duration(
  //       //   hours: d.endTime!.hour,
  //       //   minutes: d.endTime!.minute,
  //       // ));
  //       // if (businessEnd.isBefore(businessStart)) {
  //       //   businessEnd = businessEnd.add(const Duration(days: 1));
  //       // }

  //       final businessStart = _toDateTime(s, d.startTime!);
  //       var businessEnd = _toDateTime(s, d.endTime!);
  //       if (businessEnd.isBefore(businessStart)) {
  //         businessEnd = businessEnd.add(const Duration(days: 1));
  //       }

  //       if (now.isAfter(businessStart) && now.isBefore(businessEnd)) {
  //         occupiedTableNos.add(d.tableNumber);
  //       }
  //     }
  //   }

  //   final occupied = occupiedTableNos.length;
  //   final available = total - occupied;
  //   final typeStats = _buildTypeStats();

  //   return Padding(
  //     padding: const EdgeInsets.all(8),
  //     child: Row(
  //       children: [
  //         _statCard('Total', total.toString(), Icons.table_chart, Colors.blue),
  //         const SizedBox(width: 6),
  //         _statCard('Tersedia', available.toString(), Icons.check_circle, Colors.green),
  //         const SizedBox(width: 6),
  //         _statCard('Dipakai', occupied.toString(), Icons.timer, Colors.red),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildSummary() {
    final total = _tables.length;
    final now = DateTime.now();
    final occupiedTableNos = <String>{};

    for (final s in _sessions) {
      for (final d in s.billiardDetails) {
        if (d.startTime == null || d.endTime == null) continue;

        final businessStart = _toDateTime(s, d.startTime!);
        var businessEnd = _toDateTime(s, d.endTime!);
        if (businessEnd.isBefore(businessStart)) {
          businessEnd = businessEnd.add(const Duration(days: 1));
        }

        if (now.isAfter(businessStart) &&
          now.isBefore(businessEnd) &&
          !_isSessionSettled(s)        // ← tambahkan ini
      ) {
          occupiedTableNos.add(d.tableNumber);
        }
      }
    }

    final occupied = occupiedTableNos.length;
    final available = total - occupied;

    final typeStats = _buildTypeStats();

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _statCard('Total', total.toString(), Icons.table_chart, Colors.blue),
              const SizedBox(width: 6),
              _statCard('Tersedia', available.toString(), Icons.check_circle, Colors.green),
              const SizedBox(width: 6),
              _statCard('Dipakai', occupied.toString(), Icons.timer, Colors.red),
            ],
          ),
          const SizedBox(height: 12),
          typeStatsCards(_buildTypeStats()), // ← ini bro
        ],
      ),
    );
  }

  // ✅ Tambahkan ini sekali saja
  DateTime getBusinessDate(DateTime dt) {
    if (dt.hour < 3) {
      final yesterday = dt.subtract(const Duration(days: 1));
      return DateTime(yesterday.year, yesterday.month, yesterday.day);
    }
    return DateTime(dt.year, dt.month, dt.day);
  }

  // Widget _buildTableCard(dynamic table) {
  //   final now = DateTime.now();
  //   final tableNo = table['table_number'] as String;

  //   // 2) Gather all (Session,Detail) pairs for this table
  //   final entries = <MapEntry<Session, BilliardOrderDetail>>[];
  //   for (final s in _sessions) {
  //     for (final d in s.billiardDetails) {
  //       if (d.tableNumber == tableNo) {
  //         entries.add(MapEntry(s, d));
  //       }
  //     }
  //   }

  //   // 3) Sort by true session start
  //   entries.sort((a, b) {
  //     final aDate = a.key.date;
  //     final bDate = b.key.date;
  //     final aStart = DateTime(
  //       aDate.year,
  //       aDate.month,
  //       aDate.day,
  //       a.value.startTime!.hour,
  //       a.value.startTime!.minute,
  //     );
  //     final bStart = DateTime(
  //       bDate.year,
  //       bDate.month,
  //       bDate.day,
  //       b.value.startTime!.hour,
  //       b.value.startTime!.minute,
  //     );
  //     return aStart.compareTo(bStart);
  //   });

  //   // 4) Find current & next
  //   MapEntry<Session, BilliardOrderDetail>? currentEntry, nextEntry;
  //   for (final e in entries) {
  //     final s = e.key;
  //     final d = e.value;
  //     // final sessionDate = DateTime(s.date.year, s.date.month, s.date.day);
  //     final sessionBusinessDate = getBusinessDate(
  //       DateTime(s.date.year, s.date.month, s.date.day,
  //               d.startTime?.hour ?? 0, d.startTime?.minute ?? 0)
  //     );

  //     // var start = DateTime(
  //     //   sessionDate.year,
  //     //   sessionDate.month,
  //     //   sessionDate.day,
  //     //   d.startTime!.hour,
  //     //   d.startTime!.minute,
  //     // );
  //     // var end = DateTime(
  //     //   sessionDate.year,
  //     //   sessionDate.month,
  //     //   sessionDate.day,
  //     //   d.endTime!.hour,
  //     //   d.endTime!.minute,
  //     // );
  //     // if (end.isBefore(start)) end = end.add(const Duration(days: 1));

  //     final start = _toDateTime(s, d.startTime!);
  //     var end = _toDateTime(s, d.endTime!);
  //     if (end.isBefore(start)) end = end.add(const Duration(days: 1));

  //     if (now.isAfter(start) && now.isBefore(end)) {
  //       currentEntry = e;
  //     } else if (currentEntry == null && now.isBefore(start)) {
  //       nextEntry = e;
  //     }
  //   }

  //   final occupied = currentEntry != null;

  //   // 5) Compute current start/end & remaining
  //   DateTime? currentStart, currentEnd;
  //   int remaining = 0;
  //   if (occupied) {
  //     final d = currentEntry.value;
  //     final sessionDate = DateTime(
  //       currentEntry.key.date.year,
  //       currentEntry.key.date.month,
  //       currentEntry.key.date.day,
  //     );
  //     // currentStart = DateTime(
  //     //   sessionDate.year,
  //     //   sessionDate.month,
  //     //   sessionDate.day,
  //     //   d.startTime!.hour,
  //     //   d.startTime!.minute,
  //     // );
  //     // currentEnd = DateTime(
  //     //   sessionDate.year,
  //     //   sessionDate.month,
  //     //   sessionDate.day,
  //     //   d.endTime!.hour,
  //     //   d.endTime!.minute,
  //     // );
  //     // if (currentEnd.isBefore(currentStart)) {
  //     //   currentEnd = currentEnd.add(const Duration(days: 1));
  //     // }
  //     currentStart = _toDateTime(currentEntry.key, d.startTime!);
  //     currentEnd = _toDateTime(currentEntry.key, d.endTime!);
  //     if (currentEnd.isBefore(currentStart)) currentEnd = currentEnd.add(const Duration(days: 1));

  //     final diff = currentEnd.difference(now).inSeconds;
  //     remaining = diff > 0 ? diff : 0;
  //   }

  //   // 6) Compute next start/end
  //   DateTime? nextStart, nextEnd;
  //   if (nextEntry != null) {
  //     final d = nextEntry.value;
  //     final sessionDate = DateTime(
  //       nextEntry.key.date.year,
  //       nextEntry.key.date.month,
  //       nextEntry.key.date.day,
  //     );
  //     nextStart = DateTime(
  //       sessionDate.year,
  //       sessionDate.month,
  //       sessionDate.day,
  //       d.startTime!.hour,
  //       d.startTime!.minute,
  //     );
  //     nextEnd = DateTime(
  //       sessionDate.year,
  //       sessionDate.month,
  //       sessionDate.day,
  //       d.endTime!.hour,
  //       d.endTime!.minute,
  //     );
  //     if (nextEnd.isBefore(nextStart)) {
  //       nextEnd = nextEnd.add(const Duration(days: 1));
  //     }
  //   }

  //   // 7) Render the card
  //   return Card(
  //     color: occupied ? Colors.red.shade600 : Colors.green.shade600,
  //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
  //     child: Padding(
  //       padding: const EdgeInsets.all(12),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           // ── Header ──
  //           Row(
  //             children: [
  //               Icon(occupied ? Icons.timer : Icons.check_circle, color: Colors.white, size: 40),
  //               const SizedBox(width: 6),
  //               Text('Meja $tableNo',
  //                   style: const TextStyle(
  //                     color: Colors.white,
  //                     fontSize: 18,
  //                     fontWeight: FontWeight.bold,
  //                   )),
  //               _buildTypeBadge(table['type'] as String? ?? 'STANDARD'),
  //               if (!occupied)
  //                 const Padding(
  //                   padding: EdgeInsets.only(left: 8),
  //                   child: Text('(Tersedia)', style: TextStyle(color: Colors.white70, fontSize: 12)),
  //                 ),
  //             ],
  //           ),
  //           const SizedBox(height: 8),

  //           // ── Current Order ──
  //           if (occupied) ...[
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Text(
  //                   currentEntry.key.guestName,
  //                   style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
  //                 ),
  //                 Text(
  //                   _formatDuration(remaining),
  //                   style: const TextStyle(color: Colors.white70, fontWeight: FontWeight.bold, fontSize: 12),
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(height: 4),
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 _infoRow('Mulai', _formatTime(currentStart)),
  //                 _infoRow('Selesai', _formatTime(currentEnd)),
  //               ],
  //             ),
  //           ] else ...[
  //             const Text('Tersedia', style: TextStyle(color: Colors.white70, fontSize: 12)),
  //           ],

  //           // ── Next Order ──
  //           if (nextEntry != null) ...[
  //             const SizedBox(height: 10),
  //             Row(
  //               children: [
  //                 const Icon(Icons.arrow_forward, size: 14, color: Colors.white70),
  //                 const SizedBox(width: 6),
  //                 Expanded(
  //                   child: Text(
  //                     'Selanjutnya: ${_formatTime(nextStart)} – ${_formatTime(nextEnd)}',
  //                     style: const TextStyle(color: Colors.white54, fontSize: 12),
  //                     overflow: TextOverflow.ellipsis,
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ],
  //         ],
  //       ),
  //     ),
  //   );
  // }

  Widget _buildTableCard(dynamic table) {
    final now = DateTime.now();
    final tableNo = table['table_number'] as String;

    // Ambil semua entry untuk meja ini
    final entries = <MapEntry<Session, BilliardOrderDetail>>[];
    for (final s in _sessions) {
      for (final d in s.billiardDetails) {
        if (d.tableNumber == tableNo && !_isSessionSettled(s)) {   // ← tambah cek
          entries.add(MapEntry(s, d));
        }
      }
    }

    // Urutkan berdasarkan start time bisnis
    entries.sort((a, b) {
      final aStart = _toDateTime(a.key, a.value.startTime!);
      final bStart = _toDateTime(b.key, b.value.startTime!);
      return aStart.compareTo(bStart);
    });

    // Cari entry yang sedang berlangsung (current) dan yang akan datang (next)
    MapEntry<Session, BilliardOrderDetail>? currentEntry, nextEntry;
    for (final e in entries) {
      final s = e.key;
      final d = e.value;
      final start = _toDateTime(s, d.startTime!);
      var end = _toDateTime(s, d.endTime!);
      if (end.isBefore(start)) end = end.add(const Duration(days: 1));

      if (now.isAfter(start) && now.isBefore(end) && !_isSessionSettled(e.key)) {
        currentEntry = e;
      } else if (currentEntry == null && now.isBefore(start)) {
        nextEntry = e;
      }
    }

    final occupied = currentEntry != null;

    // Hitung waktu sesi aktif
    DateTime? currentStart, currentEnd;
    int remaining = 0;
    if (occupied) {
      final d = currentEntry!.value;
      currentStart = _toDateTime(currentEntry.key, d.startTime!);
      currentEnd = _toDateTime(currentEntry.key, d.endTime!);
      if (currentEnd.isBefore(currentStart)) {
        currentEnd = currentEnd.add(const Duration(days: 1));
      }
      final diff = currentEnd.difference(now).inSeconds;
      remaining = diff > 0 ? diff : 0;
    }

    // Hitung waktu sesi berikutnya
    DateTime? nextStart, nextEnd;
    if (nextEntry != null) {
      final d = nextEntry.value;
      nextStart = _toDateTime(nextEntry.key, d.startTime!);
      nextEnd = _toDateTime(nextEntry.key, d.endTime!);
      if (nextEnd.isBefore(nextStart)) {
        nextEnd = nextEnd.add(const Duration(days: 1));
      }
    }

    // Render kartu
    return Card(
      color: occupied ? Colors.red.withOpacity(0.7) : Colors.green.withOpacity(0.7),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      occupied ? Icons.timer : Icons.check_circle,
                      color: Colors.white,
                      size: 40,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Meja $tableNo',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    _buildTypeBadge(table['type'] as String? ?? 'STANDARD'),
                  ],
                ),
                const SizedBox(height: 8),

                // Info sesi aktif
                if (occupied) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        currentEntry!.key.guestName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        _formatDuration(remaining),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _infoRow('Mulai', _formatTime(currentStart)),
                      _infoRow('Selesai', _formatTime(currentEnd)),
                    ],
                  ),
                ] else ...[
                  const Text(
                    'Tersedia',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],

                // Info sesi berikutnya
                if (nextEntry != null) ...[
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      const Icon(Icons.arrow_forward, size: 14, color: Colors.white70),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          'Selanjutnya: ${_formatTime(nextStart)} – ${_formatTime(nextEnd)}',
                          style: const TextStyle(color: Colors.white54, fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Fungsi untuk memanggil endpoint yang akan mengirim email report
  Future<void> _sendReport() async {
    // untuk Android Emulator:
    final uri = Uri.parse('http://localhost:4400/api/reports/send');

    // jika di iOS Simulator / desktop Web:
    // final uri = Uri.parse('http://localhost:4400/api/reports/send');

    try {
      final resp = await http.post(uri);
      if (resp.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('📧 Report berhasil dikirim')),
        );
      } else {
        throw Exception('Status ${resp.statusCode}: ${resp.body}');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('❌ Gagal kirim report: $e')),
      );
    }
  }

  Future<void> _resendRelayCommands() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('🔄 Mengirim ulang perintah ke semua relay…')),
    );

    try {
      // 1) Ambil semua relay
      final List<Relay> relays = await _relayService.getAllRelays();

      // 2) Loop dan set ON/OFF sesuai status
      for (final r in relays) {
        final turnOn = r.status == 1; // sesuaikan field status di model Relay
        final success = await _relayService.setRelay(r.id, turnOn);
        if (!success) {
          throw Exception('Relay ${r.id} gagal diset ${turnOn ? 'ON' : 'OFF'}');
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('✅ Semua perintah relay telah dikirim ulang')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('❌ Gagal kirim ulang perintah: $e')),
      );
    }
  }

  DateTime? parseTimeToday(String? timeStr) {
    if (timeStr == null || timeStr.isEmpty) return null;
    final now = DateTime.now();
    return DateTime.tryParse('${now.toIso8601String().substring(0, 10)} $timeStr');
  }

  String _formatDuration(int seconds) {
    final h = seconds ~/ 3600;
    final m = (seconds % 3600) ~/ 60;
    final s = seconds % 60;
    return '${h}j ${m}m ${s}s';
  }

  String _formatTime(DateTime? dt) {
    if (dt == null) return '-';
    return DateFormat('HH:mm').format(dt.toLocal());
  }

  int _getRemainingSeconds(dynamic order) {
    if (order == null) return 0;
    final remaining = order['remaining_time_seconds'] ?? 0;
    if (_lastFetch == null) return remaining;
    final elapsed = DateTime.now().difference(_lastFetch!).inSeconds;
    return (remaining - elapsed).clamp(0, remaining);
  }

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('$label: ', style: const TextStyle(color: Colors.white, fontSize: 12)),
          Text(value, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12)),
        ],
      ),
    );
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final tablesFuture = _tableService.fetchRawDashboardTables();
      final sessionsFuture = _sessionService.fetchSessions();
      final results = await Future.wait([tablesFuture, sessionsFuture]);

      _tables = results[0];
      _sessions = results[1] as List<Session>;
      _lastFetch = DateTime.now();
    } catch (e) {
      _error = 'Gagal memuat data: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshData() async {
    try {
      final tablesFuture = _tableService.fetchRawDashboardTables();
      final sessionsFuture = _sessionService.fetchSessions();
      final results = await Future.wait([tablesFuture, sessionsFuture]);

      if (!mounted) return;
      setState(() {
        _tables = results[0];
        _sessions = results[1] as List<Session>;
        _lastFetch = DateTime.now();
      });
    } catch (_) {
      // you can log or ignore errors here
    }
  }

  Widget _buildTypeBadge(String type) {
    late final Color color;
    switch (type) {
      case 'VVIP':
        color = Colors.purple;
        break;
      case 'PREMIUM':
        color = Colors.blue;
        break;
      case 'PRESIDENT':
        color = Colors.orange;
        break;
      case 'EXCLUSIVE':
        color = Colors.teal;
        break;
      default:
        color = Colors.green;
    }
    return Container(
      margin: const EdgeInsets.only(left: 6),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(6)),
      child: Text(type, style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold)),
    );
  }

  Widget _statCard(String label, String count, IconData icon, Color color) {
    return Expanded(
      child: Card(
        elevation: 8,
        color: color.withOpacity(0.7),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.white, size: 32),
              const SizedBox(width: 8),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(label, style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600)),
                  Text(count, style: const TextStyle(color: Colors.white, fontSize: 26, fontWeight: FontWeight.bold)),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
