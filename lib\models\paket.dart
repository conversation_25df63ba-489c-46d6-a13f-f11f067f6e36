/// A single duration-price tier.
class PriceTier {
  int? id;
  int duration;
  double price;

  PriceTier({
    this.id,
    required this.duration,
    required this.price,
  });

  /// Parse from JSON; price may come as int or double.
  factory PriceTier.fromJson(Map<String, dynamic> json) {
    return PriceTier(
      id: json['id'] as int?,
      duration: json['duration'] as int,
      price: (json['price'] as num).toDouble(),
    );
  }

  /// Serialize back to JSON.
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{
      'duration': duration,
      'price': price,
    };
    if (id != null) {
      data['id'] = id;
    }
    return data;
  }
}

/// A package with multiple [PriceTier]s.
class RentalPackage {
  final int id;
  final String namaPaket;
  final String deskripsi;
  final List<PriceTier> harga;

  RentalPackage({
    required this.id,
    required this.namaPaket,
    required this.deskripsi,
    required this.harga,
  });

  factory RentalPackage.fromJson(Map<String, dynamic> json) {
    final rawTiers = json['harga'] as List<dynamic>? ?? <dynamic>[];
    return RentalPackage(
      id: json['id'] as int,
      namaPaket: json['nama_paket'] as String,
      deskripsi: json['deskripsi'] as String,
      harga: rawTiers.map((e) => PriceTier.fromJson(e as Map<String, dynamic>)).toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'nama_paket': namaPaket,
        'deskripsi': deskripsi,
        'harga': harga.map((t) => t.toJson()).toList(),
      };
}
