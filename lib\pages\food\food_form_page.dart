// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../data/menu_item_service.dart';
import '../../models/food.dart';

class MenuItemFormPage extends StatefulWidget {
  final Food? menuItem;

  const MenuItemFormPage({super.key, this.menuItem});

  @override
  State<MenuItemFormPage> createState() => _MenuItemFormPageState();
}

class _MenuItemFormPageState extends State<MenuItemFormPage> {
  final _formKey = GlobalKey<FormState>();

  late final FoodService apiService;

  late TextEditingController _nameCtrl;
  late TextEditingController _categoryCtrl;
  late TextEditingController _descCtrl;
  late TextEditingController _priceCtrl;
  late TextEditingController _taxCtrl;

  bool _isAvailable = true;

  bool _isLoading = false;

  bool _initialized = false;

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.menuItem != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? '✏️ Edit Item' : '➕ Tambah Item'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameCtrl,
                decoration: const InputDecoration(
                  prefixIcon: Icon(Icons.fastfood),
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                ),
                validator: (v) => (v == null || v.isEmpty) ? 'Name is required' : null,
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField(
                value: [
                  'Makanan Berat',
                  'Makanan Ringan',
                  'Minuman Panas',
                  'Minuman Dingin',
                ].contains(_categoryCtrl.text)
                    ? _categoryCtrl.text
                    : null,
                validator: (v) => (v == null || v.isEmpty) ? 'Category is required' : null,
                items: [
                  DropdownMenuItem(value: 'Makanan Berat', child: Text('Makanan Berat')),
                  DropdownMenuItem(value: 'Makanan Ringan', child: Text('Makanan Ringan')),
                  DropdownMenuItem(value: 'Minuman Panas', child: Text('Minuman Panas')),
                  DropdownMenuItem(value: 'Minuman Dingin', child: Text('Minuman Dingin')),
                ],
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                onChanged: (value) => setState(() => _categoryCtrl.text = value!),
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _priceCtrl,
                decoration: const InputDecoration(
                  labelText: 'Price',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                keyboardType: TextInputType.number,
                validator: (v) => (v == null || v.isEmpty) ? 'Price is required' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _taxCtrl,
                decoration: const InputDecoration(
                  labelText: 'Tax (%)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.percent),
                ),
                maxLength: 3,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                keyboardType: TextInputType.number,
                validator: (v) => (v == null || v.isEmpty) ? 'Tax is required' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _descCtrl,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 12),
              SwitchListTile(
                title: const Text('Available'),
                value: _isAvailable,
                onChanged: (val) => setState(() => _isAvailable = val),
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton.icon(
                      onPressed: _save,
                      icon: const Icon(Icons.save),
                      label: Text(isEdit ? '💾 Simpan Perubahan' : '✅ Tambah Item'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[700],
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 50),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        textStyle: const TextStyle(fontSize: 16),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      apiService = context.read<FoodService>();
      _initialized = true;
    }
  }

  @override
  void initState() {
    super.initState();
    final item = widget.menuItem;
    _nameCtrl = TextEditingController(text: item?.name ?? '');
    _categoryCtrl = TextEditingController(text: item?.category ?? '');
    _descCtrl = TextEditingController(text: item?.description ?? '');
    _priceCtrl = TextEditingController(text: item?.price.toString());
    _taxCtrl = TextEditingController(text: item?.tax.toString());

    _isAvailable = item?.isAvailable ?? true;
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final newItem = Food(
      id: widget.menuItem?.id ?? 0,
      name: _nameCtrl.text,
      category: _categoryCtrl.text,
      description: _descCtrl.text,
      price: double.tryParse(_priceCtrl.text) ?? 0,
      isAvailable: _isAvailable,
      tax: double.tryParse(_taxCtrl.text) ?? 0,
    );

    try {
      if (widget.menuItem == null) {
        await apiService.createFood(newItem);

        _showSnackbar('✅ Item berhasil ditambahkan', Colors.green);
      } else {
        await apiService.updateFood(newItem.id!, newItem);

        _showSnackbar('✅ Item berhasil diperbarui', Colors.blue);
      }

      Navigator.pop(context, true);
    } catch (e) {
      _showSnackbar('❌ Terjadi kesalahan: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackbar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: color, duration: const Duration(seconds: 3)),
    );
  }
}
