import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

import '../../data/food_order_service.dart';
import '../../models/food_order.dart';
import '../../models/food_order_item.dart';

class PrintFoodOrderPage extends StatefulWidget {
  final int foodOrderId;

  const PrintFoodOrderPage({super.key, required this.foodOrderId});

  @override
  State<PrintFoodOrderPage> createState() => _PrintFoodOrderPageState();
}

class _PrintFoodOrderPageState extends State<PrintFoodOrderPage> {
  @override
  Widget build(BuildContext context) {
    final double receiptWidth = 200 * PdfPageFormat.mm;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          },
        ),
        title: const Text('Print Food Order', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.green.shade700,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: PdfPreview(
        build: (format) => _generatePdf(format),
        maxPageWidth: receiptWidth,
      ),
    );
  }

  Future<Uint8List> _generatePdf(PdfPageFormat format) async {
    final pdf = pw.Document();
    final orderService = Provider.of<FoodOrderService>(context, listen: false);

    final order = await orderService.fetchFoodOrderById(widget.foodOrderId);
    final items = await orderService.fetchFoodOrderItems(widget.foodOrderId);

    final ByteData logoData = await rootBundle.load('assets/logo.jpg');
    final logo = pw.MemoryImage(logoData.buffer.asUint8List());

    final double serviceRate = 0;
    final double subtotal = items.fold(0, (sum, e) => sum + e.subtotal);
    final double service = subtotal * serviceRate;
    final double discount = 0;
    final double total = subtotal + service - discount;

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat(72 * PdfPageFormat.mm, double.infinity),
        margin: const pw.EdgeInsets.all(10),
        build: (context) => pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Center(child: pw.Image(logo, height: 70)),
            pw.SizedBox(height: 4),
            pw.Center(
              child: pw.Text('DEJAVU BILLIARD', style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
            ),
            pw.SizedBox(height: 4),
            _infoRow('Tanggal', _format(order?.orderTime)),
            _infoRow('Order ID', _generateOrderId(order!)),
            _infoRow('Kasir', 'Admin'),
            _infoRow('Cust.', order.guestName),
            _infoRow('Table', 'Meja ${order.table['table_number']}'),
            _infoRow('Batch', _getBatchRange(items)),
            pw.SizedBox(height: 6),
            pw.Center(
              child: pw.Text('Order F&B', style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Divider(),
            ...items.map((e) => pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('${e.menuItem['name']}', style: const pw.TextStyle(fontSize: 9)),
                    pw.Text('${e.quantity} x ${_currency(e.priceEach)}', style: const pw.TextStyle(fontSize: 9)),
                    pw.Text(_currency(e.subtotal), style: const pw.TextStyle(fontSize: 9)),
                  ],
                )),
            pw.Divider(),
            _infoRow('Subtotal F&B', _currency(subtotal)),
            _infoRow('Service', _currency(service)),
            _infoRow('Diskon', _currency(discount)),
            pw.Divider(),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Total Bayar', style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
                pw.Text(_currency(total), style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
              ],
            ),
            pw.SizedBox(height: 8),
            pw.Center(
              child: pw.Text('TERIMAKASIH ATAS KUNJUNGANNYA', style: pw.TextStyle(fontSize: 9)),
            ),
            pw.Center(child: pw.Text('Saran & masukan: -', style: pw.TextStyle(fontSize: 9))),
          ],
        ),
      ),
    );

    return pdf.save();
  }

  pw.Widget _infoRow(String label, String value) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(label, style: const pw.TextStyle(fontSize: 9)),
        pw.Text(value, style: const pw.TextStyle(fontSize: 9)),
      ],
    );
  }

  String _currency(num number) {
    return NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0).format(number);
  }

  String _format(DateTime? dt) {
    if (dt == null) return '-';
    return DateFormat('dd/MM/yyyy HH:mm').format(dt.toLocal());
  }

  String _generateOrderId(FoodOrder order) {
    return 'ORD-${order.id}${order.orderTime.millisecondsSinceEpoch}';
  }

  String _getBatchRange(List<FoodOrderItem> items) {
    final batches = items.map((e) => e.batch).toSet().toList()..sort();
    if (batches.isEmpty) return '-';
    if (batches.length == 1) return '${batches.first}';
    return '${batches.first}-${batches.last}';
  }
}
