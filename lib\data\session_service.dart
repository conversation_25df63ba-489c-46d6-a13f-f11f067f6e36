// ignore_for_file: depend_on_referenced_packages

import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/session.dart';

class SessionService {
  final String baseUrl;
  SessionService(this.baseUrl);

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  Future<Map<String, String>> _headers() async {
    final token = await _getToken();
    if (token == null) {
      throw Exception('Token tidak ditemukan. Harap login.');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Add current user info to session
  Future<Session> _addCurrentUserToSession(Session session) async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getInt('user_id');
    final userName = prefs.getString('full_name');

    // Get current active shift ID (optional)
    int? cashShiftId;
    try {
      // This would require CashShiftService, but to avoid circular dependency,
      // we'll let the backend handle this
    } catch (e) {
      debugPrint('Could not get current shift ID: $e');
    }

    return Session(
      id: session.id,
      guestName: session.guestName,
      guestPhone: session.guestPhone,
      invoiceNumber: session.invoiceNumber,
      date: session.date,
      status: session.status,
      totalFood: session.totalFood,
      totalBilliard: session.totalBilliard,
      discountPercent: session.discountPercent,
      discountAmount: session.discountAmount,
      taxRate: session.taxRate,
      taxAmount: session.taxAmount,
      ppnRate: session.ppnRate,
      ppnAmount: session.ppnAmount,
      totalAmount: session.totalAmount,
      paidAt: session.paidAt,
      printedAt: session.printedAt,
      paymentMethod: session.paymentMethod,
      payAmount: session.payAmount,
      foodDetails: session.foodDetails,
      billiardDetails: session.billiardDetails,
      createdAt: session.createdAt,
      handledByUserId: userId,
      handledByUserName: userName,
      cashShiftId: cashShiftId,
    );
  }

  /// Fetch all sessions (including embedded details).
  Future<List<Session>> fetchSessions() async {
    try {
      final uri = Uri.parse('$baseUrl/sessions');
      final resp = await http.get(uri, headers: await _headers()).timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw Exception('Request timeout - server tidak merespons'),
          );
      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body) as List;
        return data.map((e) => Session.fromJson(e)).toList();
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      }
      throw Exception('Failed to load sessions (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('SessionService.fetchSessions error: $e');
      rethrow;
    }
  }

  /// Fetch a single session by ID.
  Future<Session> fetchSessionById(int id) async {
    try {
      final uri = Uri.parse('$baseUrl/sessions/$id');
      final resp = await http.get(uri, headers: await _headers()).timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw Exception('Request timeout - server tidak merespons'),
          );
      if (resp.statusCode == 200) {
        final Map<String, dynamic> map = jsonDecode(resp.body) as Map<String, dynamic>;
        return Session.fromJson(map);
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      } else if (resp.statusCode == 404) {
        throw Exception('Session #$id tidak ditemukan');
      }
      throw Exception('Failed to load session #$id (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('SessionService.fetchSessionById error: $e');
      rethrow;
    }
  }

  /// Create a new session.
  Future<bool> createSession(Session s) async {
    try {
      // Get current user info and add to session
      final sessionWithUser = await _addCurrentUserToSession(s);

      final uri = Uri.parse('$baseUrl/sessions');
      final resp = await http
          .post(
            uri,
            headers: await _headers(),
            body: jsonEncode(sessionWithUser.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw Exception('Request timeout'),
          );

      if (resp.statusCode == 201) {
        return true;
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      }
      return false;
    } catch (e) {
      debugPrint('SessionService.createSession error: $e');
      rethrow;
    }
  }

  /// Update an existing session.
  Future<bool> updateSession(Session s) async {
    final uri = Uri.parse('$baseUrl/sessions/${s.id}');
    final resp = await http.put(
      uri,
      headers: await _headers(),
      body: jsonEncode(s.toJson()),
    );
    return resp.statusCode == 200;
  }

  /// Delete a session.
  Future<bool> deleteSession(int id) async {
    final uri = Uri.parse('$baseUrl/sessions/$id');
    final resp = await http.delete(uri, headers: await _headers());
    return resp.statusCode == 200;
  }

  /// Mark a session as printed (sets printed_at on the server).
  Future<bool> markPrinted(int sessionId) async {
    final uri = Uri.parse('$baseUrl/sessions/$sessionId/print');
    final resp = await http.post(uri, headers: await _headers());
    if (resp.statusCode != 200) {
      throw Exception('Failed to mark printed (Status ${resp.statusCode})');
    }
    return true;
  }

  /// (Optional) send raw ESC/POS bytes to the printer backend
  Future<void> sendRawToBackend(Uint8List raw) async {
    final uri = Uri.parse('$baseUrl/print-raw');
    final request = http.MultipartRequest('POST', uri)
      ..files.add(http.MultipartFile.fromBytes(
        'raw',
        raw,
        filename: 'order.raw',
        contentType: MediaType('application', 'octet-stream'),
      ));

    final response = await request.send();
    if (response.statusCode == 200) {
      debugPrint('✅ ESC/POS berhasil dikirim.');
    } else {
      debugPrint('❌ Gagal kirim raw: ${response.statusCode}');
    }
  }
}
