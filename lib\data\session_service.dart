// ignore_for_file: depend_on_referenced_packages

import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

import '../models/session.dart';

class SessionService {
  final String baseUrl;
  SessionService(this.baseUrl);

  Future<Map<String, String>> _headers() async {
    return {'Content-Type': 'application/json'};
  }

  /// Fetch all sessions (including embedded details).
  Future<List<Session>> fetchSessions() async {
    final uri = Uri.parse('$baseUrl/sessions');
    final resp = await http.get(uri, headers: await _headers());
    if (resp.statusCode == 200) {
      final data = jsonDecode(resp.body) as List;
      return data.map((e) => Session.fromJson(e)).toList();
    }
    throw Exception('Failed to load sessions (Status ${resp.statusCode})');
  }

  /// Fetch a single session by ID.
  Future<Session> fetchSessionById(int id) async {
    final uri = Uri.parse('$baseUrl/sessions/$id');
    final resp = await http.get(uri, headers: await _headers());
    if (resp.statusCode == 200) {
      final Map<String, dynamic> map =
          jsonDecode(resp.body) as Map<String, dynamic>;
      return Session.fromJson(map);
    }
    throw Exception('Failed to load session #$id (Status ${resp.statusCode})');
  }

  /// Create a new session.
  Future<bool> createSession(Session s) async {
    final uri = Uri.parse('$baseUrl/sessions');
    final resp = await http.post(
      uri,
      headers: await _headers(),
      body: jsonEncode(s.toJson()),
    );
    return resp.statusCode == 201;
  }

  /// Update an existing session.
  Future<bool> updateSession(Session s) async {
    final uri = Uri.parse('$baseUrl/sessions/${s.id}');
    final resp = await http.put(
      uri,
      headers: await _headers(),
      body: jsonEncode(s.toJson()),
    );
    return resp.statusCode == 200;
  }

  /// Delete a session.
  Future<bool> deleteSession(int id) async {
    final uri = Uri.parse('$baseUrl/sessions/$id');
    final resp = await http.delete(uri, headers: await _headers());
    return resp.statusCode == 200;
  }

  /// Mark a session as printed (sets printed_at on the server).
  Future<bool> markPrinted(int sessionId) async {
    final uri = Uri.parse('$baseUrl/sessions/$sessionId/print');
    final resp = await http.post(uri, headers: await _headers());
    if (resp.statusCode != 200) {
      throw Exception(
          'Failed to mark printed (Status ${resp.statusCode})');
    }
    return true;
  }

  /// (Optional) send raw ESC/POS bytes to the printer backend
  Future<void> sendRawToBackend(Uint8List raw) async {
    final uri = Uri.parse('$baseUrl/print-raw');
    final request = http.MultipartRequest('POST', uri)
      ..files.add(http.MultipartFile.fromBytes(
        'raw',
        raw,
        filename: 'order.raw',
        contentType: MediaType('application', 'octet-stream'),
      ));

    final response = await request.send();
    if (response.statusCode == 200) {
      debugPrint('✅ ESC/POS berhasil dikirim.');
    } else {
      debugPrint('❌ Gagal kirim raw: ${response.statusCode}');
    }
  }
}
