// lib/models/relay.dart

import 'dart:convert';

class Relay {
  final int id;
  final String relayName;
  int status; // 0 = OFF, 1 = ON
  final String? ipAddress;
  final String? hardwareId;

  Relay({
    required this.id,
    required this.relayName,
    required this.status,
    this.ipAddress,
    this.hardwareId,
  });

  /// Convert JSON map ke Relay
  factory Relay.fromJson(Map<String, dynamic> json) {
    return Relay(
      id: json['id'] as int,
      relayName: json['relay_name'] as String? ?? '',
      status: json['status'] as int? ?? 0,
      ipAddress: json['ip_address'] as String?,
      hardwareId: json['hardware_id'] as String?,
    );
  }

  /// Convert Relay ke JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'relay_name': relayName,
      'status': status,
      if (ipAddress != null) 'ip_address': ipAddress,
      if (hardwareId != null) 'hardware_id': hardwareId,
    };
  }

  /// Parse JSON string menjadi List<Relay>
  static List<Relay> fromJsonList(String str) {
    final List<dynamic> jsonData = json.decode(str) as List<dynamic>;
    return jsonData
        .map((e) => Relay.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  /// Encode List<Relay> menjadi JSON string
  static String toJsonList(List<Relay> list) {
    final data = list.map((e) => e.toJson()).toList();
    return json.encode(data);
  }
}
