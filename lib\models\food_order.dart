import '../../models/food_order_item.dart';

class FoodOrder {
  final int id;
  final Map<String, dynamic> table;
  final String guestName;
  final DateTime orderTime;
  final String status;
  final double totalAmount;
  List<FoodOrderItem> items;            // ← baru

  FoodOrder({
    required this.id,
    required this.table,
    required this.guestName,
    required this.orderTime,
    required this.status,
    required this.totalAmount,
    required this.items,                       // ← baru
  });

  factory FoodOrder.fromJson(Map<String, dynamic> json) => FoodOrder(
        id: json['id'] ?? 0,
        table: json['table'] ?? {},
        guestName: json['guest_name'] ?? 'Unknown',
        orderTime: DateTime.parse(json['order_time']),
        status: json['status'] ?? 'pending',
        totalAmount: double.tryParse(json['total_amount'].toString()) ?? 0.0,
        items: (json['items'] as List<dynamic>?) // ← parsing items
            ?.map((e) => FoodOrderItem.fromJson(e as Map<String, dynamic>))
            .toList() ??
          [],
      );

  Map<String, dynamic> toJson() => {
        'table': table,
        'guest_name': guestName,
        'order_time': orderTime.toIso8601String(),
        'status': status,
        'total_amount': totalAmount,
        'items': items.map((i) => i.toJson()).toList(), // ← baru
      };
}
