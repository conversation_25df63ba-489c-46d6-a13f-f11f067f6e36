/// Represents a custom price tier overridden for a specific table type.
class TypePackagePrice {
  final String tableType;
  final int packageId;
  int id;
  int duration;
  double price;
  String dayType;

  TypePackagePrice({
    required this.tableType,
    required this.packageId,
    required this.id,
    required this.duration,
    required this.price,
    this.dayType = 'weekday',
  });

  factory TypePackagePrice.fromJson(Map<String, dynamic> json) {
    return TypePackagePrice(
      tableType: json['table_type'] as String,
      packageId: json['package_id'] as int,
      id: json['id'] as int ,
      duration: json['duration'] as int,
      price: (json['price'] as num).toDouble(),
      dayType: json['day_type'] as String? ?? 'weekday',
    );
  }

  Map<String, dynamic> toJson() => {
        'table_type': tableType,
        'package_id': packageId,
        'id': id,
        'duration': duration,
        'price': price,
        'day_type': dayType,
      };
}
