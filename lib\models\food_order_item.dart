class FoodOrderItem {
  final int id;
  final int foodOrderId;
  final Map<String, dynamic> menuItem;
  int quantity;
  final double priceEach;
  double subtotal;
  final int batch;

  FoodOrderItem({
    required this.id,
    required this.foodOrderId,
    required this.menuItem,
    required this.quantity,
    required this.priceEach,
    required this.subtotal,
    required this.batch,
  });

  factory FoodOrderItem.fromJson(Map<String, dynamic> json) => FoodOrderItem(
        id: json['id'] ?? 0,
        foodOrderId: json['food_order_id'] ?? 0,
        menuItem: Map<String, dynamic>.from(json['menu_item']),
        quantity: json['quantity'] ?? 0,
        priceEach: double.tryParse(json['price_each'].toString()) ?? 0.0,
        subtotal: double.tryParse(json['subtotal'].toString()) ?? 0.0,
        batch: json['batch'] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'food_order_id': foodOrderId,
        'menu_item': menuItem,
        'quantity': quantity,
        'price_each': priceEach,
        'subtotal': subtotal,
        'batch': batch,
      };
}
