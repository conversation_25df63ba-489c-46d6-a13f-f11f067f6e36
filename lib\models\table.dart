class BilliardTable {
  final int id;
  final String tableNumber;
  final String type;
  final int status;
  final int relayId;
  final double price;

  BilliardTable({
    required this.id,
    required this.tableNumber,
    required this.type,
    required this.status,
    required this.relayId,
    required this.price,
  });

  factory BilliardTable.fromJson(Map<String, dynamic> json) => BilliardTable(
        id: json['id'] ?? 0,
        tableNumber: json['table_number'] ?? '',
        type: json['type'] ?? 'STANDARD',
        status: json['status'] ?? 0,
        relayId: json['relay_id'] ?? 0,
        price: double.tryParse(json['price'].toString()) ?? 0.0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'table_number': tableNumber,
        'type': type,
        'status': status,
        'relay_id': relayId,
        'price': price,
      };
}
