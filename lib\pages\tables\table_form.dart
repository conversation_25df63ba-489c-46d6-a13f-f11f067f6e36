// lib/features/pages/dashboard/table_form_page.dart
// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../data/relay_service.dart';
import '../../data/table_service.dart';
import '../../models/table.dart';
import '../../models/relay.dart';

class TableFormPage extends StatefulWidget {
  final BilliardTable? table;

  const TableFormPage({super.key, this.table});

  @override
  State<TableFormPage> createState() => _TableFormPageState();
}

class _TableFormPageState extends State<TableFormPage> {
  final _formKey = GlobalKey<FormState>();

  late final TableApiService apiService;
  late final RelayService relayService;

  late TextEditingController tableNumberController;
  late TextEditingController priceController;
  int status = 0;
  int? selectedRelayId;
  String? selectedType;

  bool _isLoading = false;
  bool _isFetchingRelays = true;
  List<Relay> relays = [];
  final List<String> tableTypes = [
    'STANDARD',
    'VVIP',
    'PREMIUM',
    'PRESIDENT',
    'EXCLUSIVE',
  ];

  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    final tbl = widget.table;
    tableNumberController = TextEditingController(text: tbl?.tableNumber ?? '');
    priceController = TextEditingController(text: tbl?.price.toString() ?? '0');
    selectedRelayId = tbl?.relayId;
    status = tbl?.status ?? 0;
    selectedType = tbl?.type ?? tableTypes.first;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      apiService = context.read<TableApiService>();
      relayService = context.read<RelayService>();
      _fetchRelays();
      _initialized = true;
    }
  }

  Future<void> _fetchRelays() async {
    try {
      final allRelays = await relayService.getAllRelays();
      final usedRelayIds = (await apiService.fetchTables()).map((t) => t.relayId).toSet();

      setState(() {
        relays = allRelays.where((r) => !usedRelayIds.contains(r.id) || r.id == selectedRelayId).toList();
        _isFetchingRelays = false;
        if (selectedRelayId == null && relays.isNotEmpty) {
          selectedRelayId = relays.first.id;
        }
      });
    } catch (e) {
      _showSnackbar('❌ Gagal mengambil data Relay: $e', Colors.red);
      setState(() => _isFetchingRelays = false);
    }
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) return;
    if (selectedRelayId == null) {
      _showSnackbar('❗ Pilih Relay terlebih dahulu!', Colors.red);
      return;
    }
    if (selectedType == null) {
      _showSnackbar('❗ Pilih Tipe Meja terlebih dahulu!', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    final newTable = BilliardTable(
      id: widget.table?.id ?? 0,
      tableNumber: tableNumberController.text,
      status: status,
      relayId: selectedRelayId!,
      price: double.tryParse(priceController.text) ?? 0.0,
      type: selectedType!,
    );

    try {
      if (widget.table == null) {
        await apiService.createTable(newTable);
        _showSnackbar('✅ Meja berhasil ditambahkan', Colors.green);
      } else {
        await apiService.updateTable(widget.table!.id, newTable);
        _showSnackbar('✅ Meja berhasil diperbarui', Colors.blue);
      }
      Navigator.pop(context, true);
    } catch (e) {
      _showSnackbar('❌ Terjadi kesalahan: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackbar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: color, duration: const Duration(seconds: 3)),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.table != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? '✏️ Edit Meja' : '➕ Tambah Meja'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: tableNumberController,
                decoration: const InputDecoration(
                  labelText: 'Nomor Meja',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.table_bar),
                ),
                validator: (v) => (v == null || v.isEmpty) ? '❗ Nomor Meja wajib diisi' : null,
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'Tipe Meja',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: tableTypes.map((t) => DropdownMenuItem(value: t, child: Text(t))).toList(),
                onChanged: (v) => setState(() => selectedType = v),
                validator: (v) => v == null ? '❗ Pilih Tipe Meja' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: priceController,
                decoration: const InputDecoration(
                  labelText: 'Harga Meja',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.money),
                ),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (v) => (v == null || v.isEmpty) ? '❗ Harga Meja wajib diisi' : null,
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<int>(
                value: status,
                decoration: const InputDecoration(
                  labelText: 'Status Meja',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.event_available),
                ),
                items: const [
                  DropdownMenuItem(value: 0, child: Text('✅ Tersedia')),
                  DropdownMenuItem(value: 1, child: Text('🚀 Dipakai')),
                  DropdownMenuItem(value: 2, child: Text('⚙️ Maintenance')),
                ],
                onChanged: (v) => setState(() => status = v ?? 0),
              ),
              const SizedBox(height: 12),
              _isFetchingRelays
                  ? const Center(child: LinearProgressIndicator())
                  : DropdownButtonFormField<int>(
                      value: selectedRelayId,
                      decoration: const InputDecoration(
                        labelText: 'Relay Meja',
                        border: OutlineInputBorder(),
                      ),
                      items: relays.map((r) => DropdownMenuItem(value: r.id, child: Text('${r.relayName} (ID: ${r.id})'))).toList(),
                      onChanged: (v) => setState(() => selectedRelayId = v),
                      validator: (v) => v == null ? '❗ Pilih Relay' : null,
                    ),
              const SizedBox(height: 24),
              _isLoading
                  ? const Center(child: LinearProgressIndicator())
                  : ElevatedButton.icon(
                      onPressed: _save,
                      icon: const Icon(Icons.save),
                      label: Text(isEdit ? '💾 Simpan Perubahan' : '✅ Tambah Meja'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[700],
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 50),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        textStyle: const TextStyle(fontSize: 16),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
