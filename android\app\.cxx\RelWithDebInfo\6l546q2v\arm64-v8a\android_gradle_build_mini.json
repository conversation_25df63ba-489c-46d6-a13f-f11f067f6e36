{"buildFiles": ["C:\\Program Files\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\bilyard_flutter\\android\\app\\.cxx\\RelWithDebInfo\\6l546q2v\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\bilyard_flutter\\android\\app\\.cxx\\RelWithDebInfo\\6l546q2v\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}