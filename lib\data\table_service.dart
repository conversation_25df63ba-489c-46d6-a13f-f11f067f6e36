

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/table.dart';

class TableApiService {
  final String baseUrl;
  TableApiService(this.baseUrl);

  String get _endpoint => '$baseUrl/tables';

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  Future<Map<String, String>> _buildHeaders() async {
    final token = await _getToken();
    if (token == null) {
      throw Exception('Token tidak ditemukan. Harap login.');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  Future<List<BilliardTable>> fetchTables() async {
    final uri = Uri.parse(_endpoint);

    try {
      final headers = await _buildHeaders();
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => BilliardTable.fromJson(item)).toList();
      } else {
        throw Exception('Gagal memuat daftar meja (Status ${response.statusCode})');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<BilliardTable> fetchTableById(int id) async {
    final uri = Uri.parse('$_endpoint/$id');

    try {
      final headers = await _buildHeaders();
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        return BilliardTable.fromJson(jsonDecode(response.body));
      } else if (response.statusCode == 404) {
        throw Exception('Meja tidak ditemukan');
      } else {
        throw Exception('Gagal mengambil data meja (Status ${response.statusCode})');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> createTable(BilliardTable table) async {
    final uri = Uri.parse(_endpoint);
    final body = jsonEncode(table.toJson());

    try {
      final headers = await _buildHeaders();
      final response = await http.post(uri, headers: headers, body: body);

      if (response.statusCode != 201) {
        throw Exception('Gagal menambahkan meja: ${response.body}');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateTable(int id, BilliardTable table) async {
    final uri = Uri.parse('$_endpoint/$id');
    final body = jsonEncode(table.toJson());

    try {
      final headers = await _buildHeaders();
      final response = await http.put(uri, headers: headers, body: body);

      if (response.statusCode != 200) {
        throw Exception('Gagal memperbarui meja: ${response.body}');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteTable(int id) async {
    final uri = Uri.parse('$_endpoint/$id');

    try {
      final headers = await _buildHeaders();
      final response = await http.delete(uri, headers: headers);

      if (response.statusCode != 200) {
        throw Exception('Gagal menghapus meja: ${response.body}');
      }

      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<dynamic>> fetchRawDashboardTables() async {
    final res = await http.get(Uri.parse('$baseUrl/dashboard'));

    if (res.statusCode == 200) {
      debugPrint('[TABLES] Dashboard data: ${res.body}');
      return jsonDecode(res.body) as List<dynamic>;
    } else {
      throw Exception('Gagal memuat data dashboard (${res.statusCode})');
    }
  }

  // ✅ Tambahan: Update hanya relay_status meja
  Future<void> updateRelayStatus(int tableId, int relayStatus) async {
    final uri = Uri.parse('$_endpoint/$tableId/relay-status');
    final body = jsonEncode({'relay_status': relayStatus});

    try {
      final headers = await _buildHeaders();
      final response = await http.put(uri, headers: headers, body: body);

      if (response.statusCode != 200) {
        throw Exception('Gagal update relay status meja: ${response.body}');
      }
    } catch (e) {
      rethrow;
    }
  }
}
