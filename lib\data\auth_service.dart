// ignore_for_file: avoid_print

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  final String baseUrl;
  ApiService(this.baseUrl);
  // Ensuring compatibility with Flutter Web

  /// User Login
  Future<bool> login(String username, String password) async {
    print("[LOGIN] Attempting login for username: $username");

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"username": username, "password": password}),
      );

      print("[LOGIN] Server Response: ${response.statusCode} - ${response.body}");

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        String token = data['token'];
        String fullName = data['full_name'] ?? username;
        int userId = data['user_id'] ?? 0;

        print("[LOGIN] Login successful. Token received: $token");

        // Save token and user info for future use
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', token);
        await prefs.setString('full_name', fullName);
        await prefs.setInt('user_id', userId);
        await prefs.setString('username', username);
        print("[LOGIN] Token and user info saved in SharedPreferences");

        return true;
      } else {
        print("[LOGIN] Login failed. Invalid credentials.");
        return false;
      }
    } catch (e) {
      print("[LOGIN ERROR] $e");
      return false;
    }
  }

  /// User Registration
  Future<bool> register(String username, String password, String fullName) async {
    print("[REGISTER] Registering new user: $username");

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/register'),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "username": username,
          "password": password,
          "full_name": fullName,
        }),
      );

      print("[REGISTER] Server Response: ${response.statusCode} - ${response.body}");

      return response.statusCode == 200;
    } catch (e) {
      print("[REGISTER ERROR] $e");
      return false;
    }
  }

  /// Logout User
  Future<void> logout() async {
    print("[LOGOUT] Logging out user...");

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
    await prefs.remove('full_name');
    await prefs.remove('user_id');
    await prefs.remove('username');
    print("[LOGOUT] Token and user info removed. User logged out.");
  }

  /// Get Token for Authentication
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    print("[TOKEN] Retrieved token: $token");
    return token;
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    bool loggedIn = token != null;
    print("[AUTH CHECK] User logged in: $loggedIn");
    return loggedIn;
  }

  /// Fetch authenticated user data
  Future<Map<String, dynamic>?> getUserProfile() async {
    print("[PROFILE] Fetching user profile...");

    try {
      final token = await getToken();
      if (token == null) {
        print("[PROFILE ERROR] No token found, user not logged in.");
        return null;
      }

      final response = await http.get(
        Uri.parse('$baseUrl/auth/profile'),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $token",
        },
      );

      print("[PROFILE] Server Response: ${response.statusCode} - ${response.body}");

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print("[PROFILE ERROR] Failed to fetch profile.");
        return null;
      }
    } catch (e) {
      print("[PROFILE ERROR] $e");
      return null;
    }
  }
}
