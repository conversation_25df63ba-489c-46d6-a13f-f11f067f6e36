import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/cash_shift_service.dart';
import '../../data/user_service.dart';
import '../../models/cash_shift.dart';
import '../../models/user.dart';
import 'handover_shift_page.dart';

class EndShiftPage extends StatefulWidget {
  final CashShift currentShift;

  const EndShiftPage({
    super.key,
    required this.currentShift,
  });

  @override
  State<EndShiftPage> createState() => _EndShiftPageState();
}

class _EndShiftPageState extends State<EndShiftPage> {
  final _formKey = GlobalKey<FormState>();
  final _endingCashController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Set default ending cash to calculated amount
    final calculatedCash = widget.currentShift.calculatedEndingCash;
    _endingCashController.text = NumberFormat('#,###', 'id_ID').format(calculatedCash.round());
  }

  @override
  void dispose() {
    _endingCashController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _endShift() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final cashShiftService = context.read<CashShiftService>();
      final endingCash = double.parse(_endingCashController.text.replaceAll(',', ''));

      final shift = await cashShiftService.endShift(
        endingCash: endingCash,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Shift berhasil diakhiri untuk ${widget.currentShift.userName}'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, shift);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Gagal mengakhiri shift: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);
    final shift = widget.currentShift;
    final calculatedCash = shift.calculatedEndingCash;
    final actualCash = double.tryParse(_endingCashController.text.replaceAll(',', '')) ?? calculatedCash;
    final difference = actualCash - calculatedCash;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Akhiri Shift Kas'),
        backgroundColor: Colors.orange.shade700,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.person, color: Colors.orange.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Petugas: ${shift.userName}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.access_time, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Durasi Shift: ${shift.formattedDuration}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.schedule, color: Colors.green.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Mulai: ${DateFormat('dd/MM/yyyy HH:mm').format(shift.startTime)}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Ringkasan Shift',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryRow('Kas Awal', currencyFormat.format(shift.startingCash)),
                      _buildSummaryRow('Total Penjualan Cash', currencyFormat.format(shift.totalCash ?? 0)),
                      _buildSummaryRow('Total Penjualan Debit', currencyFormat.format(shift.totalDebit ?? 0)),
                      _buildSummaryRow('Total Penjualan QRIS', currencyFormat.format(shift.totalQris ?? 0)),
                      const Divider(),
                      _buildSummaryRow(
                        'Kas Seharusnya',
                        currencyFormat.format(calculatedCash),
                        isTotal: true,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Kas Akhir Aktual',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _endingCashController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    if (newValue.text.isEmpty) return newValue;
                    final number = int.tryParse(newValue.text);
                    if (number == null) return oldValue;
                    final formatted = NumberFormat('#,###', 'id_ID').format(number);
                    return TextEditingValue(
                      text: formatted,
                      selection: TextSelection.collapsed(offset: formatted.length),
                    );
                  }),
                ],
                decoration: InputDecoration(
                  labelText: 'Jumlah Kas Akhir',
                  hintText: 'Hitung kas fisik yang ada',
                  prefixText: 'Rp ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Kas akhir harus diisi';
                  }
                  final number = double.tryParse(value.replaceAll(',', ''));
                  if (number == null || number < 0) {
                    return 'Masukkan jumlah yang valid';
                  }
                  return null;
                },
                onChanged: (value) => setState(() {}),
              ),
              const SizedBox(height: 8),
              if (difference != 0)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: difference > 0 ? Colors.green.shade50 : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: difference > 0 ? Colors.green.shade200 : Colors.red.shade200,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        difference > 0 ? Icons.trending_up : Icons.trending_down,
                        color: difference > 0 ? Colors.green.shade700 : Colors.red.shade700,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        difference > 0 ? 'Lebih: ${currencyFormat.format(difference)}' : 'Kurang: ${currencyFormat.format(-difference)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: difference > 0 ? Colors.green.shade700 : Colors.red.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Catatan Akhir Shift (Opsional)',
                  hintText: 'Catatan tentang kondisi kas, masalah, dll...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 50,
                      child: OutlinedButton(
                        onPressed: _isLoading
                            ? null
                            : () async {
                                final result = await Navigator.push<CashShift>(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => HandoverShiftPage(currentShift: widget.currentShift),
                                  ),
                                );
                                if (result != null && mounted) {
                                  Navigator.pop(context, result);
                                }
                              },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.purple.shade700,
                          side: BorderSide(color: Colors.purple.shade700),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Serah Terima',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: SizedBox(
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _endShift,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange.shade700,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text(
                                'Akhiri Shift',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.green.shade700 : null,
            ),
          ),
        ],
      ),
    );
  }
}
