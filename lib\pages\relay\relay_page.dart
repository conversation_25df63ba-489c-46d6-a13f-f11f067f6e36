// lib/pages/relay/relay_page.dart

// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/relay.dart';
import '../../data/relay_service.dart';

class RelayPage extends StatefulWidget {
  const RelayPage({super.key});

  @override
  State<RelayPage> createState() => _RelayPageState();
}

class _RelayPageState extends State<RelayPage> {
  late RelayService _relayService;
  late Future<List<Relay>> _futureRelays;

  bool _seqRunning = false;
  bool _oddRunning = false;
  bool _evenRunning = false;
  bool _seqCancel = false; // untuk membatalkan sequential
  int? _currentSeqIndex;

  @override
  void initState() {
    super.initState();
    _relayService = context.read<RelayService>();
    _futureRelays = _relayService.getAllRelays();
  }

  Future<void> _refreshRelays() async {
    final fresh = await _relayService.getAllRelays();
    setState(() => _futureRelays = Future.value(fresh));
  }

  Future<void> _toggleRelay(Relay relay) async {
    final newStatus = relay.status == 0;
    final success = await _relayService.setRelay(relay.id, newStatus);
    if (success) {
      setState(() => relay.status = newStatus ? 1 : 0);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal mengubah status ${relay.relayName}')),
      );
    }
  }

  Future<void> _startSequential() async {
    // If already running, this was triggered by the switch → request cancel
    if (_seqRunning) {
      setState(() => _seqCancel = true);
      return;
    }

    setState(() {
      _seqRunning = true;
      _seqCancel = false;
      _currentSeqIndex = null;
    });

    final relays = await _relayService.getAllRelays();
    int? lastIndex;
    for (int i = 0; i < relays.length; i++) {
      if (_seqCancel) break;

      final relay = relays[i];
      lastIndex = i;
      setState(() => _currentSeqIndex = i + 1);

      // TURN ON
      await _relayService.setRelay(relay.id, true);
      await Future.delayed(const Duration(seconds: 2));

      // if cancel requested immediately after ON, turn this one OFF then break
      if (_seqCancel) {
        await _relayService.setRelay(relay.id, false);
        break;
      }

      // TURN OFF
      await _relayService.setRelay(relay.id, false);
      await Future.delayed(const Duration(seconds: 2));
    }

    // In case the loop ended normally (no cancel) but left something on—
    // or if cancel right before starting any ON, make sure last is off:
    if (_seqCancel && lastIndex != null) {
      final relay = relays[lastIndex];
      if (relay.status == 1) {
        await _relayService.setRelay(relay.id, false);
      }
    }

    setState(() {
      _seqRunning = false;
      _seqCancel = false;
      _currentSeqIndex = null;
    });
  }

  Future<void> _startOdd() async {
    if (_oddRunning) return;
    setState(() => _oddRunning = true);

    final relays = await _relayService.getAllRelays();
    final odds = relays.where((r) => r.id % 2 == 1).toList();

    await Future.wait(odds.map((r) => _relayService.setRelay(r.id, true)));
    await Future.delayed(const Duration(seconds: 2));
    await Future.wait(odds.map((r) => _relayService.setRelay(r.id, false)));

    setState(() => _oddRunning = false);
  }

  Future<void> _startEven() async {
    if (_evenRunning) return;
    setState(() => _evenRunning = true);

    final relays = await _relayService.getAllRelays();
    final evens = relays.where((r) => r.id % 2 == 0).toList();

    await Future.wait(evens.map((r) => _relayService.setRelay(r.id, true)));
    await Future.delayed(const Duration(seconds: 2));
    await Future.wait(evens.map((r) => _relayService.setRelay(r.id, false)));

    setState(() => _evenRunning = false);
  }

  Widget _buildTestCard({
    required String title,
    required bool running,
    required VoidCallback onTap,
    required bool showSwitch,
    String? subtitle,
    required IconData icon,
  }) {
    return Expanded(
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 2,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: running && !showSwitch ? null : onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, size: 32, color: running ? Colors.green : Colors.grey),
                const SizedBox(height: 8),
                Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(subtitle, style: const TextStyle(fontSize: 12)),
                ],
                if (showSwitch) ...[
                  const SizedBox(height: 8),
                  Switch(
                    value: _seqRunning,
                    onChanged: (_) => _startSequential(),
                    activeColor: Colors.green,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Relay Control'),
        backgroundColor: Colors.green.shade700,
      ),
      body: Column(
        children: [
          // Testing cards
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                _buildTestCard(
                  title: 'Sequential',
                  icon: Icons.repeat,
                  running: _seqRunning,
                  subtitle: _seqRunning ? 'Step #${_currentSeqIndex}' : 'Idle',
                  onTap: _startSequential,
                  showSwitch: true,
                ),
                _buildTestCard(
                  title: 'Ganjil',
                  icon: Icons.filter_1,
                  running: _oddRunning,
                  subtitle: _oddRunning ? 'Running…' : 'Idle',
                  onTap: _startOdd,
                  showSwitch: false,
                ),
                _buildTestCard(
                  title: 'Genap',
                  icon: Icons.filter_2,
                  running: _evenRunning,
                  subtitle: _evenRunning ? 'Running…' : 'Idle',
                  onTap: _startEven,
                  showSwitch: false,
                ),
              ],
            ),
          ),

          // List relay
          Expanded(
            child: FutureBuilder<List<Relay>>(
              future: _futureRelays,
              builder: (context, snap) {
                if (snap.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snap.hasError) {
                  return Center(child: Text('Error: ${snap.error}'));
                }
                final relays = snap.data!;
                if (relays.isEmpty) {
                  return const Center(child: Text('Tidak ada data relay'));
                }
                return RefreshIndicator(
                  onRefresh: _refreshRelays,
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: relays.length,
                    itemBuilder: (context, i) {
                      final r = relays[i];
                      return Card(
                        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                        child: ListTile(
                          title: Text(r.relayName),
                          trailing: Switch(
                            value: r.status == 1,
                            activeColor: Colors.green,
                            onChanged: (_) => _toggleRelay(r),
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
