import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../data/holiday_service.dart';
import '../../models/holiday.dart';
import 'holiday_form_page.dart';

class HolidayListPage extends StatefulWidget {
  @override
  _HolidayListPageState createState() => _HolidayListPageState();
}

class _HolidayListPageState extends State<HolidayListPage> {
  late final HolidayService _svc;
  List<Holiday> _holidays = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _load());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _svc = context.read<HolidayService>();
    //_load();
  }

  Future<void> _load() async {
    try {
      final all = await _svc.getAll();
      print('[HOLIDAY] fetched ${all.length} items: $all');
      setState(() => _holidays = all);
    } catch (e) {
      print('[HOLIDAY] fetch error: $e');
    }
  }

  Future<void> _confirmDelete(Holiday h) async {
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Hapus Hari Besar?'),
        content: Text('Yakin mau menghapus "${h.name}" pada ${DateFormat('yyyy-MM-dd').format(h.holidayDate)}?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Batal')),
          ElevatedButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('Hapus')),
        ],
      ),
    );

    if (ok == true) {
      try {
        await _svc.delete(h.id!);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Berhasil menghapus "${h.name}".')),
        );
        _load();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Gagal menghapus: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext ctx) {
    return Scaffold(
      appBar: AppBar(title: Text('Hari Besar')),
      body: _holidays.isEmpty
          ? Center(child: Text('Belum ada hari besar.'))
          : ListView.builder(
              itemCount: _holidays.length,
              itemBuilder: (_, i) {
                final h = _holidays[i];
                final date = h.holidayDate;
                return ListTile(
                  key: ValueKey(h),
                  title: Text(h.name),
                  subtitle: Text(DateFormat.yMMMMd().format(h.holidayDate)),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min, // ← limit row to the size of its children
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit),
                        onPressed: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(builder: (_) => HolidayFormPage(h)),
                          );
                          _load();
                        },
                      ),
                      IconButton(
                        icon: Icon(Icons.delete),
                        onPressed: () => _confirmDelete(h),
                      ),
                    ],
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        child: Icon(Icons.add),
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => HolidayFormPage(null)),
          );
          _load();
        },
      ),
    );
  }
}
