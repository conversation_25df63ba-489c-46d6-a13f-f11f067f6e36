// models/holiday.dart
class Holiday {
  final int?    id;
  final DateTime holidayDate;
  final String name;

  Holiday({ this.id, required this.holidayDate, required this.name });

  factory Holiday.fromJson(Map<String, dynamic> json) => Holiday(
    id:          json['id'] as int?,
    holidayDate: DateTime.parse(json['holiday_date'] as String),
    name:        json['name'] as String,
  );

  Map<String, dynamic> toJson() => {
    // MATCHES what your controller expects:
    'holiday_date': holidayDate.toIso8601String().split('T').first, // atau format sesuai backend
    'name':         name,
  };
}
