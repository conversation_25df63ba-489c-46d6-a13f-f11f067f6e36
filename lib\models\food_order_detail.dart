// lib/models/food_order_detail.dart

class FoodOrderDetail {
  final int id;
  // final int? foodOrderId;
  final int foodId;
  final String foodName;
  final int batch;
  final int quantity;
  final double priceEach;
  final double subtotal;

  FoodOrderDetail({
    required this.id,
    // required this.foodOrderId,
    required this.foodId,
    required this.foodName,
    required this.batch,
    required this.quantity,
    required this.priceEach,
    required this.subtotal,
  });

  factory FoodOrderDetail.fromJson(Map<String, dynamic> json) {
    return FoodOrderDetail(
      id: json['id'] as int,
      // foodOrderId: json['food_order_id'] as int?,
      foodId: json['food_id'] as int,
      // Safely handle possibly missing 'food_name'
      foodName: (json['food_name'] as String?) ?? '',
      batch: json['batch'] as int,
      quantity: json['quantity'] as int,
      priceEach: double.tryParse(json['price_each'].toString()) ?? 0.0,
      subtotal: double.tryParse(json['subtotal'].toString()) ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // 'food_order_id': foodOrderId,
      'food_id': foodId,
      'batch': batch,
      'quantity': quantity,
      'price_each': priceEach,
      'subtotal': subtotal,
      // Include name if needed on serialization
      'food_name': foodName,
    };
  }
}
