// lib/data/menu_item_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/food.dart';

class FoodService {
  final String baseUrl;
  FoodService(this.baseUrl);

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  Future<Map<String, String>> _buildHeaders() async {
    final token = await _getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Fetch all menu items
  Future<List<Food>> fetchFoods() async {
    final uri = Uri.parse('$baseUrl/menu_items');
    final resp = await http.get(uri, headers: await _buildHeaders());
    if (resp.statusCode == 200) {
      final List<dynamic> data = jsonDecode(resp.body);
      return data.map((e) => Food.fromJson(e)).toList();
    }
    throw Exception('Failed to fetch menu items (status ${resp.statusCode})');
  }

  /// Fetch one by ID
  Future<Food> getFoodById(int id) async {
    final uri = Uri.parse('$baseUrl/menu_items/$id');
    final resp = await http.get(uri, headers: await _buildHeaders());
    if (resp.statusCode == 200) {
      return Food.fromJson(jsonDecode(resp.body));
    }
    throw Exception('Menu item not found (status ${resp.statusCode})');
  }

  /// Create a new menu item
  Future<bool> createFood(Food item) async {
    final uri = Uri.parse('$baseUrl/menu_items');
    final resp = await http.post(
      uri,
      headers: await _buildHeaders(),
      body: jsonEncode(item.toJson()),
    );
    return resp.statusCode == 201;
  }

  /// Update an existing menu item
  Future<bool> updateFood(int id, Food item) async {
    final uri = Uri.parse('$baseUrl/menu_items/$id');
    final resp = await http.put(
      uri,
      headers: await _buildHeaders(),
      body: jsonEncode(item.toJson()),
    );
    return resp.statusCode == 200;
  }

  /// Delete a menu item
  Future<bool> deleteFood(int id) async {
    final uri = Uri.parse('$baseUrl/menu_items/$id');
    final resp = await http.delete(uri, headers: await _buildHeaders());
    return resp.statusCode == 200;
  }
}
