// lib/data/order_service.dart
// ignore_for_file: avoid_print

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/order.dart';
import '../models/table.dart';

class OrderService {
  final String baseUrl;
  OrderService(this.baseUrl);

  /// ✅ Ambil token dari SharedPreferences
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  /// ✅ Headers standar dengan Authorization token
  Future<Map<String, String>> _buildHeaders() async {
    final token = await _getToken();
    if (token == null) {
      throw Exception("Token tidak ditemukan. Harap login.");
    }
    return {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token",
    };
  }

  /// ✅ Ambil semua pesanan (opsional filter berdasarkan status)
  Future<List<Order>> fetchOrders({int? status}) async {
    try {
      final headers = await _buildHeaders();
      String url = "$baseUrl/orders";
      if (status != null) {
        url += "?status=$status";
      }

      final response = await http.get(Uri.parse(url), headers: headers).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Order.fromJson(json)).toList();
      } else {
        throw Exception("Gagal mengambil pesanan (Status ${response.statusCode})");
      }
    } catch (e) {
      print("[ORDER SERVICE] Error fetchOrders: $e");
      return [];
    }
  }

  /// ✅ Ambil semua meja (bisa filter meja tersedia)
  Future<List<BilliardTable>> fetchTables({bool onlyAvailable = false}) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.get(Uri.parse("$baseUrl/tables"), headers: headers).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        var tables = data.map((json) => BilliardTable.fromJson(json)).toList();

        if (onlyAvailable) {
          tables = tables.where((t) => t.status == 0).toList();
        }
        return tables;
      } else {
        throw Exception("Gagal mengambil daftar meja (Status ${response.statusCode})");
      }
    } catch (e) {
      print("[ORDER SERVICE] Error fetchTables: $e");
      return [];
    }
  }

  /// ✅ Buat pesanan baru
  Future<bool> createOrder(Order order) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.post(Uri.parse("$baseUrl/orders"), headers: headers, body: jsonEncode(order.toJson())).timeout(const Duration(seconds: 10));

      if (response.statusCode == 201) {
        print("[ORDER SERVICE] Pesanan berhasil dibuat");
        return true;
      } else {
        print("[ORDER SERVICE] Gagal membuat pesanan: ${response.body}");
        return false;
      }
    } catch (e) {
      print("[ORDER SERVICE] Error createOrder: $e");
      return false;
    }
  }

  /// ✅ Ambil detail pesanan berdasarkan ID
  Future<Order?> getOrderById(int id) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.get(Uri.parse("$baseUrl/orders/$id"), headers: headers).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return Order.fromJson(jsonDecode(response.body));
      } else if (response.statusCode == 404) {
        print("[ORDER SERVICE] Pesanan ID $id tidak ditemukan");
        return null;
      } else {
        throw Exception("Gagal mengambil detail pesanan (Status ${response.statusCode})");
      }
    } catch (e) {
      print("[ORDER SERVICE] Error getOrderById: $e");
      return null;
    }
  }

  /// ✅ Update pesanan
  Future<bool> updateOrder(int id, Order order) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.put(Uri.parse("$baseUrl/orders/$id"), headers: headers, body: jsonEncode(order.toJson())).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        print("[ORDER SERVICE] Pesanan berhasil diperbarui");
        return true;
      } else {
        print("[ORDER SERVICE] Gagal update pesanan: ${response.body}");
        return false;
      }
    } catch (e) {
      print("[ORDER SERVICE] Error updateOrder: $e");
      return false;
    }
  }

  /// ✅ Hapus pesanan
  Future<bool> deleteOrder(int id) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.delete(Uri.parse("$baseUrl/orders/$id"), headers: headers).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        print("[ORDER SERVICE] Pesanan berhasil dihapus");
        return true;
      } else {
        print("[ORDER SERVICE] Gagal menghapus pesanan: ${response.body}");
        return false;
      }
    } catch (e) {
      print("[ORDER SERVICE] Error deleteOrder: $e");
      return false;
    }
  }

  /// ✅ Cetak pesanan (trigger relay simulasi)
  Future<bool> printOrder(int id) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.post(Uri.parse("$baseUrl/orders/$id/print"), headers: headers).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        print("[ORDER SERVICE] Relay berhasil dijalankan untuk pesanan $id");
        return true;
      } else {
        print("[ORDER SERVICE] Gagal jalankan relay: ${response.body}");
        return false;
      }
    } catch (e) {
      print("[ORDER SERVICE] Error printOrder: $e");
      return false;
    }
  }

  /// ✅ Selesaikan pesanan
  Future<bool> completeOrder(int orderId) async {
    try {
      final headers = await _buildHeaders();
      final response = await http.post(Uri.parse('$baseUrl/orders/complete/$orderId'), headers: headers).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print("[ORDER SERVICE] ✅ Order $orderId berhasil diselesaikan: ${responseData['message']}");
        return true;
      } else {
        final errorData = response.body.isNotEmpty ? jsonDecode(response.body) : {'error': 'Unknown error'};
        print("[ORDER SERVICE] ❌ Gagal menyelesaikan order $orderId: ${errorData['error']}");
        return false;
      }
    } catch (e) {
      print("[ORDER SERVICE] ❌ Error completeOrder: $e");
      return false;
    }
  }
}
