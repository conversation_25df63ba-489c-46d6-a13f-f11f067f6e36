import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cash_shift.dart';

class CashShiftService {
  final String baseUrl;
  CashShiftService(this.baseUrl);

  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  Future<Map<String, String>> _headers() async {
    final token = await _getToken();
    if (token == null) {
      throw Exception('Token tidak ditemukan. Harap login.');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Get current active shift for logged in user
  Future<CashShift?> getCurrentActiveShift() async {
    try {
      final uri = Uri.parse('$baseUrl/cash-shifts/current');
      final resp = await http.get(uri, headers: await _headers()).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body);
        if (data != null) {
          return CashShift.fromJson(data as Map<String, dynamic>);
        }
        return null;
      } else if (resp.statusCode == 404) {
        return null; // No active shift
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      }
      throw Exception('Failed to get current shift (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('CashShiftService.getCurrentActiveShift error: $e');
      rethrow;
    }
  }

  /// Start a new shift
  Future<CashShift> startShift({
    required double startingCash,
    String? notes,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl/cash-shifts');
      final body = jsonEncode({
        'starting_cash': startingCash,
        'notes': notes,
      });
      
      final resp = await http.post(
        uri,
        headers: await _headers(),
        body: body,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      if (resp.statusCode == 201) {
        final data = jsonDecode(resp.body) as Map<String, dynamic>;
        return CashShift.fromJson(data);
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      } else if (resp.statusCode == 400) {
        final error = jsonDecode(resp.body);
        throw Exception(error['message'] ?? 'Bad request');
      }
      throw Exception('Failed to start shift (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('CashShiftService.startShift error: $e');
      rethrow;
    }
  }

  /// End current shift
  Future<CashShift> endShift({
    required double endingCash,
    String? notes,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl/cash-shifts/end');
      final body = jsonEncode({
        'ending_cash': endingCash,
        'notes': notes,
      });
      
      final resp = await http.post(
        uri,
        headers: await _headers(),
        body: body,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body) as Map<String, dynamic>;
        return CashShift.fromJson(data);
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      } else if (resp.statusCode == 400) {
        final error = jsonDecode(resp.body);
        throw Exception(error['message'] ?? 'Bad request');
      }
      throw Exception('Failed to end shift (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('CashShiftService.endShift error: $e');
      rethrow;
    }
  }

  /// Hand over shift to another user
  Future<CashShift> handOverShift({
    required int toUserId,
    required double endingCash,
    required double newStartingCash,
    String? notes,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl/cash-shifts/handover');
      final body = jsonEncode({
        'to_user_id': toUserId,
        'ending_cash': endingCash,
        'new_starting_cash': newStartingCash,
        'notes': notes,
      });
      
      final resp = await http.post(
        uri,
        headers: await _headers(),
        body: body,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body) as Map<String, dynamic>;
        return CashShift.fromJson(data);
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      } else if (resp.statusCode == 400) {
        final error = jsonDecode(resp.body);
        throw Exception(error['message'] ?? 'Bad request');
      }
      throw Exception('Failed to hand over shift (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('CashShiftService.handOverShift error: $e');
      rethrow;
    }
  }

  /// Get shift history
  Future<List<CashShift>> getShiftHistory({
    DateTime? startDate,
    DateTime? endDate,
    int? userId,
    String? status,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };
      
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String();
      }
      if (userId != null) {
        queryParams['user_id'] = userId.toString();
      }
      if (status != null) {
        queryParams['status'] = status;
      }
      
      final uri = Uri.parse('$baseUrl/cash-shifts').replace(queryParameters: queryParams);
      final resp = await http.get(uri, headers: await _headers()).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body) as List;
        return data.map((e) => CashShift.fromJson(e as Map<String, dynamic>)).toList();
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      }
      throw Exception('Failed to get shift history (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('CashShiftService.getShiftHistory error: $e');
      rethrow;
    }
  }

  /// Get shift summary/report
  Future<ShiftSummary> getShiftSummary({
    required DateTime startDate,
    required DateTime endDate,
    String period = 'daily',
    int? userId,
  }) async {
    try {
      final queryParams = <String, String>{
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'period': period,
      };
      
      if (userId != null) {
        queryParams['user_id'] = userId.toString();
      }
      
      final uri = Uri.parse('$baseUrl/cash-shifts/summary').replace(queryParameters: queryParams);
      final resp = await http.get(uri, headers: await _headers()).timeout(
        const Duration(seconds: 15),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body) as Map<String, dynamic>;
        return ShiftSummary.fromJson(data);
      } else if (resp.statusCode == 401) {
        throw Exception('Unauthorized - silakan login ulang');
      }
      throw Exception('Failed to get shift summary (Status ${resp.statusCode})');
    } catch (e) {
      debugPrint('CashShiftService.getShiftSummary error: $e');
      rethrow;
    }
  }

  /// Update shift notes
  Future<bool> updateShiftNotes(int shiftId, String notes) async {
    try {
      final uri = Uri.parse('$baseUrl/cash-shifts/$shiftId/notes');
      final body = jsonEncode({'notes': notes});
      
      final resp = await http.put(
        uri,
        headers: await _headers(),
        body: body,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw Exception('Request timeout'),
      );
      
      return resp.statusCode == 200;
    } catch (e) {
      debugPrint('CashShiftService.updateShiftNotes error: $e');
      rethrow;
    }
  }
}
