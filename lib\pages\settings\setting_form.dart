// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/setting_service.dart';
import '../../models/setting.dart';

class SettingForm extends StatefulWidget {
  const SettingForm({super.key});

  @override
  State<SettingForm> createState() => _SettingFormState();
}

class _SettingFormState extends State<SettingForm> {
  final _controllers = <int, TextEditingController>{};
  late final SettingService _service;
  late Future<List<Setting>> _future;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pengaturan'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: FutureBuilder<List<Setting>>(
          future: _future,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const Center(child: Text('Tidak ada data.'));
            }

            final settings = snapshot.data!;
            return ListView.builder(
              itemCount: settings.length,
              itemBuilder: (context, index) {
                final setting = settings[index];
                _controllers.putIfAbsent(setting.id, () => TextEditingController(text: setting.value));
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Card(
                    child: ListTile(
                      title: Text(setting.name),
                      subtitle: TextField(
                        controller: _controllers[setting.id],
                        decoration: const InputDecoration(border: OutlineInputBorder()),
                        onChanged: (val) => setting.value = val,
                      ),
                      trailing: SizedBox(
                        height: 40,
                        child: ElevatedButton.icon(
                          label: const Icon(Icons.send),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[700],
                            foregroundColor: Colors.white,
                          ),
                          onPressed: () => _save(setting),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _service = context.read<SettingService>();
    _future = _service.fetchSettings();
  }

  Future<void> _save(Setting setting) async {
    try {
      await _service.updateSetting(setting.id, setting.value);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${setting.name} berhasil disimpan')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal menyimpan ${setting.name}')),
      );
    }
  }
}
