class CashShift {
  final int id;
  final int userId;
  final String userName;
  final DateTime startTime;
  final DateTime? endTime;
  final double startingCash;
  final double? endingCash;
  final double? totalSales;
  final double? totalCash;
  final double? totalDebit;
  final double? totalQris;
  final String status; // 'active', 'completed', 'handed_over'
  final String? notes;
  final int? handedOverToUserId;
  final String? handedOverToUserName;
  final DateTime? handedOverAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CashShift({
    required this.id,
    required this.userId,
    required this.userName,
    required this.startTime,
    this.endTime,
    required this.startingCash,
    this.endingCash,
    this.totalSales,
    this.totalCash,
    this.totalDebit,
    this.totalQris,
    this.status = 'active',
    this.notes,
    this.handedOverToUserId,
    this.handedOverToUserName,
    this.handedOverAt,
    this.createdAt,
    this.updatedAt,
  });

  factory CashShift.fromJson(Map<String, dynamic> json) {
    return CashShift(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      userName: json['user_name'] ?? '',
      startTime: json['start_time'] != null 
        ? DateTime.parse(json['start_time']).toLocal()
        : DateTime.now(),
      endTime: json['end_time'] != null 
        ? DateTime.parse(json['end_time']).toLocal()
        : null,
      startingCash: double.tryParse(json['starting_cash'].toString()) ?? 0.0,
      endingCash: json['ending_cash'] != null 
        ? double.tryParse(json['ending_cash'].toString()) 
        : null,
      totalSales: json['total_sales'] != null 
        ? double.tryParse(json['total_sales'].toString()) 
        : null,
      totalCash: json['total_cash'] != null 
        ? double.tryParse(json['total_cash'].toString()) 
        : null,
      totalDebit: json['total_debit'] != null 
        ? double.tryParse(json['total_debit'].toString()) 
        : null,
      totalQris: json['total_qris'] != null 
        ? double.tryParse(json['total_qris'].toString()) 
        : null,
      status: json['status'] ?? 'active',
      notes: json['notes'],
      handedOverToUserId: json['handed_over_to_user_id'],
      handedOverToUserName: json['handed_over_to_user_name'],
      handedOverAt: json['handed_over_at'] != null 
        ? DateTime.parse(json['handed_over_at']).toLocal()
        : null,
      createdAt: json['created_at'] != null 
        ? DateTime.parse(json['created_at']).toLocal()
        : null,
      updatedAt: json['updated_at'] != null 
        ? DateTime.parse(json['updated_at']).toLocal()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'user_name': userName,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'starting_cash': startingCash,
      'ending_cash': endingCash,
      'total_sales': totalSales,
      'total_cash': totalCash,
      'total_debit': totalDebit,
      'total_qris': totalQris,
      'status': status,
      'notes': notes,
      'handed_over_to_user_id': handedOverToUserId,
      'handed_over_to_user_name': handedOverToUserName,
      'handed_over_at': handedOverAt?.toIso8601String(),
    };
  }

  // Helper methods
  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isHandedOver => status == 'handed_over';

  double get calculatedEndingCash {
    if (endingCash != null) return endingCash!;
    return startingCash + (totalCash ?? 0.0);
  }

  double get cashDifference {
    return calculatedEndingCash - startingCash - (totalCash ?? 0.0);
  }

  Duration get shiftDuration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  String get formattedDuration {
    final duration = shiftDuration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  CashShift copyWith({
    int? id,
    int? userId,
    String? userName,
    DateTime? startTime,
    DateTime? endTime,
    double? startingCash,
    double? endingCash,
    double? totalSales,
    double? totalCash,
    double? totalDebit,
    double? totalQris,
    String? status,
    String? notes,
    int? handedOverToUserId,
    String? handedOverToUserName,
    DateTime? handedOverAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashShift(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      startingCash: startingCash ?? this.startingCash,
      endingCash: endingCash ?? this.endingCash,
      totalSales: totalSales ?? this.totalSales,
      totalCash: totalCash ?? this.totalCash,
      totalDebit: totalDebit ?? this.totalDebit,
      totalQris: totalQris ?? this.totalQris,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      handedOverToUserId: handedOverToUserId ?? this.handedOverToUserId,
      handedOverToUserName: handedOverToUserName ?? this.handedOverToUserName,
      handedOverAt: handedOverAt ?? this.handedOverAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Model untuk shift summary/report
class ShiftSummary {
  final String period; // 'daily', 'weekly', 'monthly'
  final DateTime startDate;
  final DateTime endDate;
  final List<CashShift> shifts;
  final double totalSales;
  final double totalCash;
  final double totalDebit;
  final double totalQris;
  final Map<String, double> salesByUser;
  final Map<String, int> sessionCountByUser;

  ShiftSummary({
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.shifts,
    required this.totalSales,
    required this.totalCash,
    required this.totalDebit,
    required this.totalQris,
    required this.salesByUser,
    required this.sessionCountByUser,
  });

  factory ShiftSummary.fromJson(Map<String, dynamic> json) {
    return ShiftSummary(
      period: json['period'] ?? 'daily',
      startDate: DateTime.parse(json['start_date']).toLocal(),
      endDate: DateTime.parse(json['end_date']).toLocal(),
      shifts: (json['shifts'] as List<dynamic>?)
          ?.map((e) => CashShift.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      totalSales: double.tryParse(json['total_sales'].toString()) ?? 0.0,
      totalCash: double.tryParse(json['total_cash'].toString()) ?? 0.0,
      totalDebit: double.tryParse(json['total_debit'].toString()) ?? 0.0,
      totalQris: double.tryParse(json['total_qris'].toString()) ?? 0.0,
      salesByUser: Map<String, double>.from(
        (json['sales_by_user'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, double.tryParse(value.toString()) ?? 0.0),
        ) ?? {},
      ),
      sessionCountByUser: Map<String, int>.from(
        (json['session_count_by_user'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, int.tryParse(value.toString()) ?? 0),
        ) ?? {},
      ),
    );
  }
}
