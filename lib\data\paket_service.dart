// lib/data/rental_package_service.dart

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/paket.dart';

class RentalPackageService {
  final String baseUrl;
  RentalPackageService(this.baseUrl);

  Future<List<RentalPackage>> getAllPackages() async {
    final res = await http.get(
      Uri.parse('$baseUrl/packages'),
      headers: {'Content-Type': 'application/json'},
    );
    if (res.statusCode != 200) {
      throw Exception('Gagal mengambil paket (Status ${res.statusCode})');
    }
    final List<dynamic> data = jsonDecode(res.body);
    return data.map((e) => RentalPackage.fromJson(e)).toList();
  }

  Future<RentalPackage> getPackageById(int id) async {
    final res = await http.get(
      Uri.parse('$baseUrl/packages/$id'),
      headers: {'Content-Type': 'application/json'},
    );
    if (res.statusCode != 200) {
      throw Exception('Paket tidak ditemukan (Status ${res.statusCode})');
    }
    return RentalPackage.fromJson(jsonDecode(res.body));
  }

  Future<bool> createPackage(RentalPackage pkg) async {
    final res = await http.post(
      Uri.parse('$baseUrl/packages'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'nama_paket': pkg.namaPaket,
        'deskripsi':  pkg.deskripsi,
        'harga':      pkg.harga.map((t) => t.toJson()).toList(),
      }),
    );
    if (res.statusCode != 201) {
      throw Exception('Gagal menambahkan paket (Status ${res.statusCode})');
    }
    return true;
  }

  Future<bool> updatePackage(int id, RentalPackage pkg) async {
    final res = await http.put(
      Uri.parse('$baseUrl/packages/$id'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'nama_paket': pkg.namaPaket,
        'deskripsi':  pkg.deskripsi,
        'harga':      pkg.harga.map((t) => t.toJson()).toList(),
      }),
    );
    if (res.statusCode != 200) {
      throw Exception('Gagal memperbarui paket (Status ${res.statusCode})');
    }
    return true;
  }

  Future<bool> deletePackage(int id) async {
    final res = await http.delete(
      Uri.parse('$baseUrl/packages/$id'),
      headers: {'Content-Type': 'application/json'},
    );
    if (res.statusCode != 200) {
      throw Exception('Gagal menghapus paket (Status ${res.statusCode})');
    }
    return true;
  }
}
