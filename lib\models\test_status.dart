class TestStatus {
  final bool sequential;
  final bool odd;
  final bool even;
  final int? currentIndex;

  TestStatus({
    required this.sequential,
    required this.odd,
    required this.even,
    this.currentIndex,
  });

  factory TestStatus.fromJson(Map<String, dynamic> json) {
    return TestStatus(
      sequential: json['sequential'] as bool? ?? false,
      odd:        json['odd']        as bool? ?? false,
      even:       json['even']       as bool? ?? false,
      currentIndex: (json['currentIndex'] is int)
        ? json['currentIndex'] as int
        : int.tryParse(json['currentIndex']?.toString() ?? ''),
    );
  }
}
