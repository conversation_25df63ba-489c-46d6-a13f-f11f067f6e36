// lib/data/rental_shift_service.dart

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/shift.dart';

class RentalShiftService {
  final String baseUrl;
  RentalShiftService(this.baseUrl);

  String get _endpoint => '$baseUrl/shifts';

  /// ✅ 1. Get all shifts
  Future<List<RentalShift>> getAllShifts() async {
    final uri = Uri.parse(_endpoint);
    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final List<dynamic> jsonData = jsonDecode(response.body);
      return jsonData.map((item) => RentalShift.fromJson(item)).toList();
    } else {
      throw Exception(
        'Gagal mengambil shift rental (Status ${response.statusCode})',
      );
    }
  }

  /// ✅ 2. Get shift by ID
  Future<RentalShift> getShiftById(int id) async {
    final uri = Uri.parse('$_endpoint/$id');
    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      return RentalShift.fromJson(jsonDecode(response.body));
    } else {
      throw Exception(
        'Shift tidak ditemukan (Status ${response.statusCode})',
      );
    }
  }

  /// ✅ 3. Create new shift
  Future<bool> createShift(RentalShift shift) async {
    final uri = Uri.parse(_endpoint);
    final response = await http.post(
      uri,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(shift.toJson()),
    );

    return response.statusCode == 201;
  }

  /// ✅ 4. Update shift
  Future<bool> updateShift(int id, RentalShift shift) async {
    final uri = Uri.parse('$_endpoint/$id');
    final response = await http.put(
      uri,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(shift.toJson()),
    );

    return response.statusCode == 200;
  }

  /// ✅ 5. Delete shift
  Future<bool> deleteShift(int id) async {
    final uri = Uri.parse('$_endpoint/$id');
    final response = await http.delete(
      uri,
      headers: {'Content-Type': 'application/json'},
    );

    return response.statusCode == 200;
  }
}
