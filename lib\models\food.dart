class Food {
  final int? id;
  final String name;
  final String category;
  final String description;
  final double price;
  final bool isAvailable;
  final double discountPercent;
  final double tax;

  Food({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.price,
    required this.isAvailable,
    this.discountPercent = 0.0,
    this.tax = 0.0,
  });

  factory Food.fromJson(Map<String, dynamic> json) => Food(
        id: json['id'] as int?,
        name: json['name'] as String,
        category: json['category'] as String,
        description: json['description'] as String,
        price: double.tryParse(json['price'].toString()) ?? 0,
        isAvailable: json['is_available'] == 1,
        discountPercent: double.tryParse(json['discount_percent']?.toString() ?? '0') ?? 0,
        tax: double.tryParse(json['tax']?.toString() ?? '0') ?? 0,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'category': category,
        'description': description,
        'price': price,
        'is_available': isAvailable ? 1 : 0,
        'discount_percent': discountPercent,
        'tax': tax,
      };
}
