// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../users/users_page.dart';
import '../../screens/login_page.dart';
import 'setting_form.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _userName = 'Guest';
  String _userEmail = '<EMAIL>';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Pengaturan',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
        ),
        backgroundColor: Colors.green.shade700,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildProfileHeader(),
          const Divider(),
          _buildListTile(
            title: '<PERSON>il <PERSON><PERSON>',
            icon: Icons.person,
            onTap: () {},
          ),
          _buildListTile(
            title: 'Users',
            icon: Icons.people,
            onTap: () => _promptPasswordAndNavigate(() => const UsersPage()),
          ),
          _buildListTile(
            title: 'Settings',
            icon: Icons.settings,
            onTap: () => _promptPasswordAndNavigate(() => const SettingForm()),
          ),
          _buildListTile(
            title: 'Keluar',
            icon: Icons.exit_to_app,
            color: Colors.red,
            onTap: () {
              _showLogoutConfirmation();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _promptPasswordAndNavigate(Widget Function() targetPageBuilder) async {
    final controller = TextEditingController();
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Masukkan Password'),
          content: TextField(
            controller: controller,
            obscureText: true,
            decoration: const InputDecoration(labelText: 'Password'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Batal'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text == 'admin') {
                  Navigator.of(context).pop(true);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Password salah'), backgroundColor: Colors.red),
                  );
                }
              },
              child: const Text('Lanjut'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => targetPageBuilder()),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Widget _buildListTile({
    required String title,
    required IconData icon,
    Color color = Colors.black87,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title, style: TextStyle(fontSize: 16, color: color)),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
      onTap: onTap,
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        CircleAvatar(
          radius: 40,
          backgroundColor: Colors.green.shade700,
          child: const Icon(Icons.person, size: 50, color: Colors.white),
        ),
        const SizedBox(height: 10),
        Text(
          _userName,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(
          _userEmail,
          style: const TextStyle(fontSize: 14, color: Colors.grey),
        ),
      ],
    );
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _userName = prefs.getString('full_name') ?? 'Guest';
      _userEmail = prefs.getString('email') ?? '<EMAIL>';
    });
  }

  Future<void> _logout() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove('token');
    await prefs.remove('is_logged_in');
    await prefs.remove('full_name');
    await prefs.remove('email');

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Berhasil logout'), backgroundColor: Colors.green),
    );

    await Future.delayed(const Duration(milliseconds: 500));

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => LoginPage()),
      (route) => false,
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Konfirmasi Keluar'),
        content: const Text('Apakah Anda yakin ingin keluar?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _logout();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Keluar'),
          ),
        ],
      ),
    );
  }
}
