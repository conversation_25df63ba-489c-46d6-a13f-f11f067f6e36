import 'dart:convert';

import 'package:http/http.dart' as http;

import '../models/user.dart';

class UserService {
  final String baseUrl;
  UserService(this.baseUrl);

  Future<List<User>> fetchUser() async {
    final res = await http.get(Uri.parse('$baseUrl/users'));

    if (res.statusCode == 200) {
      final data = json.decode(res.body);

      return data.map<User>((json) => User.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load user');
    }
  }

  Future<User> fetchUserById(int id) async {
    final res = await http.get(Uri.parse('$baseUrl/users/$id'));

    if (res.statusCode == 200) {
      final data = json.decode(res.body);

      return User.fromJson(data);
    } else {
      throw Exception('Failed to load user');
    }
  }

  Future<bool> createUser(User user) async {
    final res = await http.post(
      Uri.parse('$baseUrl/users'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(user.toJson()),
    );

    if (res.statusCode == 201) {
      return true;
    } else {
      throw Exception('Failed to create user');
    }
  }

  Future<bool> updateUser(User user) async {
    final res = await http.put(
      Uri.parse('$baseUrl/users/${user.id}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode(user.toJson()),
    );

    if (res.statusCode == 200) {
      return true;
    } else {
      throw Exception('Failed to update user');
    }
  }

  Future<bool> deleteUser(int id) async {
    final res = await http.delete(
      Uri.parse('$baseUrl/users/$id'),
      headers: {'Content-Type': 'application/json'},
    );

    if (res.statusCode == 200) {
      return true;
    } else {
      throw Exception('Failed to delete user');
    }
  }
}
