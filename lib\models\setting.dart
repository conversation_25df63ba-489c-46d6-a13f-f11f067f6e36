class Setting {
  final int id;
  final String name;
  String value;

  Setting({
    required this.id,
    required this.name,
    required this.value,
  });

  factory Setting.fromJson(Map<String, dynamic> json) => Setting(
        id: json['id'] ?? 0,
        name: json['name'] ?? '',
        value: json['value'] ?? '',
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'value': value,
      };
}
