import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../data/cash_shift_service.dart';
import '../../models/cash_shift.dart';

class ShiftReportPage extends StatefulWidget {
  const ShiftReportPage({super.key});

  @override
  State<ShiftReportPage> createState() => _ShiftReportPageState();
}

class _ShiftReportPageState extends State<ShiftReportPage> {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();
  String _selectedPeriod = 'daily';
  bool _isLoading = false;
  ShiftSummary? _summary;
  String? _error;

  final List<String> _periods = ['daily', 'weekly', 'monthly'];

  @override
  void initState() {
    super.initState();
    _loadReport();
  }

  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cashShiftService = context.read<CashShiftService>();
      final summary = await cashShiftService.getShiftSummary(
        startDate: _startDate,
        endDate: _endDate,
        period: _selectedPeriod,
      );

      setState(() {
        _summary = summary;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadReport();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'id_ID', symbol: 'Rp ', decimalDigits: 0);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Laporan Shift'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade100,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: _selectDateRange,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.date_range, color: Colors.blue.shade700),
                              const SizedBox(width: 8),
                              Text(
                                '${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    DropdownButton<String>(
                      value: _selectedPeriod,
                      items: _periods.map((period) {
                        return DropdownMenuItem<String>(
                          value: period,
                          child: Text(period.toUpperCase()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedPeriod = value;
                          });
                          _loadReport();
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Content Section
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error_outline, size: 64, color: Colors.red.shade300),
                            const SizedBox(height: 16),
                            Text('Error: $_error'),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadReport,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _summary == null
                        ? const Center(child: Text('No data available'))
                        : SingleChildScrollView(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Summary Cards
                                Row(
                                  children: [
                                    Expanded(
                                      child: _buildSummaryCard(
                                        'Total Penjualan',
                                        currencyFormat.format(_summary!.totalSales),
                                        Icons.attach_money,
                                        Colors.green,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: _buildSummaryCard(
                                        'Total Shift',
                                        '${_summary!.shifts.length}',
                                        Icons.access_time,
                                        Colors.blue,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),

                                Row(
                                  children: [
                                    Expanded(
                                      child: _buildSummaryCard(
                                        'Cash',
                                        currencyFormat.format(_summary!.totalCash),
                                        Icons.money,
                                        Colors.orange,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: _buildSummaryCard(
                                        'Non-Cash',
                                        currencyFormat.format(_summary!.totalDebit + _summary!.totalQris),
                                        Icons.credit_card,
                                        Colors.purple,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 24),

                                // Sales by User
                                Text(
                                  'Penjualan per User',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 12),

                                if (_summary!.salesByUser.isEmpty)
                                  const Card(
                                    child: Padding(
                                      padding: EdgeInsets.all(16),
                                      child: Text('Tidak ada data penjualan per user'),
                                    ),
                                  )
                                else
                                  ..._summary!.salesByUser.entries.map((entry) {
                                    final sessionCount = _summary!.sessionCountByUser[entry.key] ?? 0;
                                    return Card(
                                      child: ListTile(
                                        leading: CircleAvatar(
                                          backgroundColor: Colors.blue.shade700,
                                          child: Text(
                                            entry.key.substring(0, 1).toUpperCase(),
                                            style: const TextStyle(color: Colors.white),
                                          ),
                                        ),
                                        title: Text(
                                          entry.key,
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        subtitle: Text('$sessionCount transaksi'),
                                        trailing: Text(
                                          currencyFormat.format(entry.value),
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green.shade700,
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),

                                const SizedBox(height: 24),

                                // Shift History
                                Text(
                                  'Riwayat Shift',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 12),

                                if (_summary!.shifts.isEmpty)
                                  const Card(
                                    child: Padding(
                                      padding: EdgeInsets.all(16),
                                      child: Text('Tidak ada data shift'),
                                    ),
                                  )
                                else
                                  ..._summary!.shifts.map((shift) {
                                    return Card(
                                      child: ExpansionTile(
                                        leading: Icon(
                                          shift.isActive ? Icons.play_circle : Icons.stop_circle,
                                          color: shift.isActive ? Colors.green : Colors.grey,
                                        ),
                                        title: Text(shift.userName),
                                        subtitle: Text(
                                          '${DateFormat('dd/MM/yyyy HH:mm').format(shift.startTime)} - ${shift.endTime != null ? DateFormat('HH:mm').format(shift.endTime!) : 'Aktif'}',
                                        ),
                                        trailing: Text(
                                          currencyFormat.format(shift.totalSales ?? 0),
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.all(16),
                                            child: Column(
                                              children: [
                                                _buildShiftDetailRow('Kas Awal', currencyFormat.format(shift.startingCash)),
                                                _buildShiftDetailRow('Kas Akhir', currencyFormat.format(shift.endingCash ?? 0)),
                                                _buildShiftDetailRow('Penjualan Cash', currencyFormat.format(shift.totalCash ?? 0)),
                                                _buildShiftDetailRow('Penjualan Debit', currencyFormat.format(shift.totalDebit ?? 0)),
                                                _buildShiftDetailRow('Penjualan QRIS', currencyFormat.format(shift.totalQris ?? 0)),
                                                _buildShiftDetailRow('Durasi', shift.formattedDuration),
                                                if (shift.notes != null && shift.notes!.isNotEmpty)
                                                  _buildShiftDetailRow('Catatan', shift.notes!),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                              ],
                            ),
                          ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShiftDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
