// data/holiday_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/holiday.dart';

class HolidayService {
  final String apiUrl;
  HolidayService(this.apiUrl);

  Future<List<Holiday>> getAll() async {
    final res = await http.get(Uri.parse('$apiUrl/holidays'));
    final li = jsonDecode(res.body) as List;
    return li.map((j)=>Holiday.fromJson(j)).toList();
  }
  Future<void> create(Holiday h) async {
    final res =await http.post(
      Uri.parse('$apiUrl/holidays'),
      headers: {'Content-Type':'application/json'},
      body: jsonEncode(h.toJson()),
    );
    if (res.statusCode != 201) {
      throw Exception('Failed to create holiday: ${res.body}');
    }
    
  }

  Future<void> delete(int id) async {
  final res = await http.delete(Uri.parse('$apiUrl/holidays/$id'));
  if (res.statusCode != 204) {
    throw Exception('Gagal menghapus holiday (status ${res.statusCode})');
  }
}

  Future<void> update(Holiday h) async {
    await http.put(
      Uri.parse('$apiUrl/holidays/${h.id}'),
      headers: {'Content-Type':'application/json'},
      body: jsonEncode(h.toJson()),
    );
  }
}
