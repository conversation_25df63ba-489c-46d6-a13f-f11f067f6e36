// ignore_for_file: use_build_context_synchronously

import 'package:bilyard_flutter/pages/users/user_form.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/user_service.dart';
import '../../models/user.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  late final UserService apiService;

  bool _initialized = false;

  String searchQuery = '';

  List<User> allUsers = [];
  List<User> filteredUsers = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      appBar: AppBar(
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        elevation: 4,
        title: const Text(
          'Master Data Users',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Data',
            onPressed: () async => await _loadUsers(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    decoration: InputDecoration(
                      labelText: 'Search',
                      prefixIcon: const Icon(Icons.search),
                      contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    onChanged: (value) => setState(() {
                      searchQuery = value;
                      _applyFilters();
                    }),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: () => _openForm(),
                    icon: const Icon(Icons.add),
                    label: const Text('Tambah'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 18),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Expanded(
              child: filteredUsers.isEmpty
                  ? const Center(child: Text('Tidak ada data user'))
                  : ListView.builder(
                      itemCount: filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = filteredUsers[index];
                        return Card(
                          child: ListTile(
                            onTap: () => _openForm(user: user),
                            title: Text(user.name, style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.black)),
                            subtitle: Text(user.username),
                            trailing: ElevatedButton.icon(
                              label: const Icon(Icons.delete),
                              onPressed: () => _confirmDelete(user.id, user.name),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red[700],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 18),
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _confirmDelete(int id, String userName) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Konfirmasi Hapus'),
        content: Text('Apakah kamu yakin ingin menghapus User $userName?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Batal')),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Hapus', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
    if (confirm == true) _deleteUser(id);
  }

  Future<void> _deleteUser(int id) async {
    try {
      await apiService.deleteUser(id);

      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('User berhasil dihapus')));

      _loadUsers();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Gagal menghapus user: $e')));
    }
  }

  @override
  void initState() {
    super.initState();

    if (!_initialized) {
      apiService = context.read<UserService>();
      _loadUsers();
      _initialized = true;
    }
  }

  Future<void> _loadUsers() async {
    try {
      final users = await apiService.fetchUser();

      setState(() {
        allUsers = users;
        _applyFilters();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Gagal memuat data user: $e')),
      );
    }
  }

  void _applyFilters() {
    setState(() {
      filteredUsers = allUsers
          .where(
            (user) => user.name.toLowerCase().contains(searchQuery.toLowerCase()) || user.username.toLowerCase().contains(searchQuery.toLowerCase()),
          )
          .toList();
    });
  }

  Future<void> _openForm({User? user}) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (_) => UserForm(user: user)),
    );
    if (result == true) {
      _loadUsers();
    }
  }
}
