// lib/features/pages/packages/paket_form.dart
// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/paket_service.dart';
import '../../models/paket.dart';

class PackageFormPage extends StatefulWidget {
  final RentalPackage? package;
  const PackageFormPage({super.key, this.package});

  @override
  State<PackageFormPage> createState() => _PackageFormPageState();
}

class _PackageFormPageState extends State<PackageFormPage> {
  final _formKey = GlobalKey<FormState>();
  late final RentalPackageService apiService;
  late TextEditingController nameCtrl;
  late TextEditingController descCtrl;
  bool _isLoading = false;

  /// Kita akan mengelola daftar PriceTier beserta controller-nya:
  List<_TierLine> _tierLines = [];

  @override
  void initState() {
    super.initState();
    // Inisialisasi controller untuk nama dan deskripsi (bisa kosong jika baru)
    nameCtrl = TextEditingController(text: widget.package?.namaPaket ?? '');
    descCtrl = TextEditingController(text: widget.package?.deskripsi ?? '');

    // Jika kita sedang edit (widget.package != null), ambil daftar PriceTier-nya:
    if (widget.package != null) {
      for (var tier in widget.package!.harga) {
        _tierLines.add(
          _TierLine(
            id: tier.id,
            durationCtrl: TextEditingController(text: tier.duration.toString()),
            priceCtrl: TextEditingController(text: tier.price.toStringAsFixed(0)),
          ),
        );
      }
    } else {
      // Jika tidak edit, mulai dengan satu baris kosong
      _tierLines.add(
        _TierLine(
          id: null,
          durationCtrl: TextEditingController(text: '1'),
          priceCtrl: TextEditingController(text: '0'),
        ),
      );
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    apiService = context.read<RentalPackageService>();
  }

  @override
  void dispose() {
    nameCtrl.dispose();
    descCtrl.dispose();
    for (var line in _tierLines) {
      line.durationCtrl.dispose();
      line.priceCtrl.dispose();
    }
    super.dispose();
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);

    final isEdit = widget.package != null;

    // Bangun list<PriceTier> dari controller:
    final List<PriceTier> hargaList = [];
    for (var line in _tierLines) {
      final dur = int.tryParse(line.durationCtrl.text.trim()) ?? 0;
      final pr  = double.tryParse(line.priceCtrl.text.trim()) ?? 0.0;
      if (dur > 0) {
        hargaList.add(PriceTier(
          id: line.id, // jika id null → backend akan insert baru
          duration: dur,
          price: pr,
        ));
      }
    }

    // Bangun objek RentalPackage
    final pkg = RentalPackage(
      id: widget.package?.id ?? 0,
      namaPaket: nameCtrl.text.trim(),
      deskripsi: descCtrl.text.trim(),
      harga: hargaList,
    );

    try {
      if (isEdit) {
        await apiService.updatePackage(pkg.id, pkg);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Paket berhasil diperbarui'),
            backgroundColor: Colors.blue,
          ),
        );
      } else {
        await apiService.createPackage(pkg);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Paket berhasil ditambahkan'),
            backgroundColor: Colors.green,
          ),
        );
      }
      if (!mounted) return;
      Navigator.pop(context, true);
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Terjadi kesalahan: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (!mounted) return;
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.package != null;
    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? 'Edit Paket' : 'Tambah Paket'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              // ───────────────────────────────────────
              // Nama Paket
              TextFormField(
                controller: nameCtrl,
                decoration: const InputDecoration(
                  labelText: 'Nama Paket',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.card_giftcard),
                ),
                validator: (v) =>
                    (v?.trim().isEmpty ?? true) ? 'Nama Paket wajib diisi' : null,
              ),
              const SizedBox(height: 12),

              // ───────────────────────────────────────
              // Deskripsi Paket
              TextFormField(
                controller: descCtrl,
                decoration: const InputDecoration(
                  labelText: 'Deskripsi',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                validator: (v) =>
                    (v?.trim().isEmpty ?? true) ? 'Deskripsi wajib diisi' : null,
              ),
              const SizedBox(height: 16),

              // ───────────────────────────────────────
              // Harga per Durasi
              Text(
                'Harga per Durasi (jam)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),

              // Daftar baris durasi/harga (dynamic)
              ..._tierLines.asMap().entries.map((entry) {
                final idx = entry.key;
                final line = entry.value;
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      // Durasi (jam)
                      Expanded(
                        child: TextFormField(
                          controller: line.durationCtrl,
                          decoration: const InputDecoration(
                            labelText: 'Durasi (jam)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (v) {
                            final x = int.tryParse(v?.trim() ?? '');
                            if (x == null || x <= 0) {
                              return '≥ 1 jam';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Harga
                      Expanded(
                        child: TextFormField(
                          controller: line.priceCtrl,
                          decoration: const InputDecoration(
                            labelText: 'Harga',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (v) {
                            final x = double.tryParse(v?.trim() ?? '');
                            if (x == null || x < 0) {
                              return '≥ 0';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Tombol hapus baris
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _tierLines.removeAt(idx);
                          });
                        },
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                    ],
                  ),
                );
              }),

              // Tombol tambah baris baru
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _tierLines.add(
                      _TierLine(
                        id: null,
                        durationCtrl: TextEditingController(text: '1'),
                        priceCtrl: TextEditingController(text: '0'),
                      ),
                    );
                  });
                },
                icon: const Icon(Icons.add, color: Colors.green),
                label: const Text(
                  'Tambah Durasi',
                  style: TextStyle(color: Colors.green),
                ),
              ),
              const SizedBox(height: 24),

              // Tombol Simpan / Loading Indicator
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton.icon(
                      onPressed: _save,
                      icon: const Icon(Icons.save),
                      label: Text(isEdit ? 'Simpan Perubahan' : 'Tambah Paket'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[700],
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 50),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Kelas pembantu untuk mengelola TextEditingController tiap baris durasi+harga
class _TierLine {
  final int? id;
  final TextEditingController durationCtrl;
  final TextEditingController priceCtrl;

  _TierLine({
    required this.id,
    required this.durationCtrl,
    required this.priceCtrl,
  });
}
