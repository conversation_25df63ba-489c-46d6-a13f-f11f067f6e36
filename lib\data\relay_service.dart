// lib/data/relay_service.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/relay.dart';

class RelayService {
  /// Base URL API-mu (sertakan `/api`, misal "http://localhost:4400/api")
  final String baseUrl;
  RelayService(this.baseUrl);

  /// Endpoint utama untuk relay
  String get _endpoint => '$baseUrl/relays';

  /// Ambil list semua relay
  Future<List<Relay>> getAllRelays() async {
    final uri = Uri.parse(_endpoint);
    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    );
    if (response.statusCode == 200) {
      return Relay.fromJsonList(response.body);
    }
    throw Exception('Gagal mengambil data relay (status ${response.statusCode})');
  }

  /// Ambil detail relay by ID
  Future<Relay> getRelayById(int id) async {
    final uri = Uri.parse('$_endpoint/$id');
    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    );
    if (response.statusCode == 200) {
      return Relay.fromJson(jsonDecode(response.body));
    }
    throw Exception('Gagal mengambil relay $id (status ${response.statusCode})');
  }

  /// Buat relay baru
  Future<bool> createRelay(Relay relay) async {
    final uri = Uri.parse(_endpoint);
    final response = await http.post(
      uri,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(relay.toJson()),
    );
    return response.statusCode == 201;
  }

  /// Update relay
  Future<bool> updateRelay(int id, Relay relay) async {
    final uri = Uri.parse('$_endpoint/$id');
    final response = await http.put(
      uri,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(relay.toJson()),
    );
    return response.statusCode == 200;
  }

  /// Hapus relay
  Future<bool> deleteRelay(int id) async {
    final uri = Uri.parse('$_endpoint/$id');
    final response = await http.delete(
      uri,
      headers: {'Content-Type': 'application/json'},
    );
    return response.statusCode == 200;
  }

  /// Kirim perintah ON/OFF ke relay via `/send`
  // Future<bool> setRelay(int id, bool turnOn) async {
  //   final uri = Uri.parse('$_endpoint/$id/send');
  //   final response = await http.post(
  //     uri,
  //     headers: {'Content-Type': 'application/json'},
  //     body: jsonEncode({'action': turnOn ? 'on' : 'off'}),
  //   );
  //   return response.statusCode == 200;
  // }

  Future<bool> setRelay(int id, bool turnOn) async {
  try {
    final uri = Uri.parse('$_endpoint/$id/send');
    final response = await http
        .post(
          uri,
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'action': turnOn ? 'on' : 'off'}),
        )
        .timeout(const Duration(seconds: 4)); // ⬅️ penting

    if (response.statusCode == 200) return true;

    // log sedikit buat debug
    debugPrint('setRelay($id, ${turnOn ? 'ON' : 'OFF'}) ⇒ HTTP ${response.statusCode}');
    return false;
  } catch (e) {
    debugPrint('setRelay($id) error: $e'); // termasuk timeout
    return false;
  }
}


  // ─────────────────────────────────────────
  // Client-side test methods (tanpa backend)
  // ─────────────────────────────────────────

  /// Sequential test: nyalakan relay satu per satu dengan delay, lalu matikan
  Future<void> testSequential({
    Duration onDuration = const Duration(seconds: 2),
    Duration offDuration = const Duration(seconds: 2),
    bool stopOnError = false,
  }) async {
    final relays = await getAllRelays();
    for (final relay in relays) {
      // TURN ON
      final okOn = await setRelay(relay.id, true);
      if (!okOn && stopOnError) break;
      await Future.delayed(onDuration);

      // TURN OFF
      final okOff = await setRelay(relay.id, false);
      if (!okOff && stopOnError) break;
      await Future.delayed(offDuration);
    }
  }

  /// Test relay ganjil: ON semua ganjil, delay, lalu OFF semua ganjil
  Future<void> testOdd({Duration delay = const Duration(seconds: 2)}) async {
    final relays = await getAllRelays();
    final odds = relays.where((r) => r.id % 2 == 1);
    await Future.wait(odds.map((r) => setRelay(r.id, true)));
    await Future.delayed(delay);
    await Future.wait(odds.map((r) => setRelay(r.id, false)));
  }

  /// Test relay genap: ON semua genap, delay, lalu OFF semua genap
  Future<void> testEven({Duration delay = const Duration(seconds: 2)}) async {
    final relays = await getAllRelays();
    final evens = relays.where((r) => r.id % 2 == 0);
    await Future.wait(evens.map((r) => setRelay(r.id, true)));
    await Future.delayed(delay);
    await Future.wait(evens.map((r) => setRelay(r.id, false)));
  }
}
